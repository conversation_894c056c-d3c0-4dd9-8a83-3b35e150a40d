using Core.DataAccess.EntityFramework;
using Core.Utilities.Security.CompanyContext; // ICompanyContext için eklendi
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Threading;


namespace DataAccess.Concrete.EntityFramework
{
    public class EfExpenseDal : EfCompanyEntityRepositoryBase<Expense, GymContext>, IExpenseDal
    {
        private readonly ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfExpenseDal(ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        // Async implementasyonlar
        public async Task<PaginatedResult<ExpenseDto>> GetExpensesPaginatedAsync(ExpensePagingParameters parameters, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            IQueryable<Expense> query = _context.Expenses.AsNoTracking()
                .Where(e => e.CompanyID == companyId && e.IsActive == true);

            query = ApplyFilters(query, parameters);

            var totalCount = await query.CountAsync(ct);

            query = ApplySorting(query, parameters);

            var expenses = await query
                .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                .Take(parameters.PageSize)
                .Select(e => new ExpenseDto
                {
                    ExpenseID = e.ExpenseID,
                    CompanyID = e.CompanyID,
                    Description = e.Description,
                    Amount = e.Amount,
                    ExpenseDate = e.ExpenseDate,
                    ExpenseType = e.ExpenseType,
                    CreationDate = e.CreationDate
                })
                .ToListAsync(ct);

            return new PaginatedResult<ExpenseDto>(expenses, parameters.PageNumber, parameters.PageSize, totalCount);
        }

        public async Task<List<ExpenseDto>> GetAllExpensesFilteredAsync(ExpensePagingParameters parameters, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            IQueryable<Expense> query = _context.Expenses.AsNoTracking()
                .Where(e => e.CompanyID == companyId && e.IsActive == true);

            query = ApplyFilters(query, parameters);
            query = ApplySorting(query, parameters);

            return await query
                .Select(e => new ExpenseDto
                {
                    ExpenseID = e.ExpenseID,
                    CompanyID = e.CompanyID,
                    Description = e.Description,
                    Amount = e.Amount,
                    ExpenseDate = e.ExpenseDate,
                    ExpenseType = e.ExpenseType,
                    CreationDate = e.CreationDate
                })
                .ToListAsync(ct);
        }

        public async Task<List<ExpenseDto>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            endDate = endDate.Date.AddDays(1).AddTicks(-1);

            return await _context.Expenses.AsNoTracking()
                .Where(e => e.CompanyID == companyId && e.IsActive == true && e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                .Select(e => new ExpenseDto
                {
                    ExpenseID = e.ExpenseID,
                    CompanyID = e.CompanyID,
                    Description = e.Description,
                    Amount = e.Amount,
                    ExpenseDate = e.ExpenseDate,
                    ExpenseType = e.ExpenseType,
                    CreationDate = e.CreationDate
                })
                .OrderByDescending(e => e.ExpenseDate)
                .ToListAsync(ct);
        }

        public async Task<ExpenseDashboardDto> GetExpenseDashboardDataAsync(int year, int month, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = new ExpenseDashboardDto
            {
                SelectedYear = year,
                SelectedMonth = month,
                DataRetrievedAt = DateTime.UtcNow
            };

            var today = DateTime.Today;
            var todayStart = today;
            var todayEnd = today.AddDays(1).AddTicks(-1);

            var monthStart = new DateTime(year, month, 1);
            var monthEnd = monthStart.AddMonths(1).AddTicks(-1);

            var yearStart = new DateTime(year, 1, 1);
            var yearEnd = new DateTime(year, 12, 31, 23, 59, 59, 999);

            var yearlyExpenses = await _context.Expenses.AsNoTracking()
                .Where(e => e.CompanyID == companyId && e.IsActive == true && e.ExpenseDate >= yearStart && e.ExpenseDate <= yearEnd)
                .Select(e => new { e.ExpenseID, e.Description, e.Amount, e.ExpenseDate, e.ExpenseType, e.CreationDate, e.CompanyID })
                .ToListAsync(ct);

            result.TotalDailyExpense = yearlyExpenses
                .Where(e => e.ExpenseDate >= todayStart && e.ExpenseDate <= todayEnd)
                .Sum(e => e.Amount);

            var monthlyExpenses = yearlyExpenses
                .Where(e => e.ExpenseDate >= monthStart && e.ExpenseDate <= monthEnd)
                .ToList();

            result.TotalMonthlyExpense = monthlyExpenses.Sum(e => e.Amount);

            result.MonthlyExpenseDetails = monthlyExpenses
                .Select(e => new ExpenseDto
                {
                    ExpenseID = e.ExpenseID,
                    CompanyID = e.CompanyID,
                    Description = e.Description,
                    Amount = e.Amount,
                    ExpenseDate = e.ExpenseDate,
                    ExpenseType = e.ExpenseType,
                    CreationDate = e.CreationDate
                })
                .OrderByDescending(e => e.ExpenseDate)
                .ToList();

            result.TotalYearlyExpense = yearlyExpenses.Sum(e => e.Amount);

            var monthlyTotals = yearlyExpenses
                .GroupBy(e => e.ExpenseDate.Month)
                .ToDictionary(g => g.Key, g => g.Sum(e => e.Amount));

            for (int m = 1; m <= 12; m++)
            {
                result.MonthlyExpenseSummary[m] = monthlyTotals.ContainsKey(m) ? monthlyTotals[m] : 0m;
            }

            return result;
            }


        public async Task<MonthlyExpenseDto> GetMonthlyExpenseAsync(int year, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            try
            {
                if (year <= 0)
                {
                    year = DateTime.Now.Year;
                }

                var result = new MonthlyExpenseDto
                {
                    Year = year,
                    Months = new List<string> { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" }
                };

                var yearlyExpenses = await _context.Expenses.AsNoTracking()
                    .Where(e => e.CompanyID == companyId && e.IsActive == true && e.ExpenseDate.Year == year)
                    .ToListAsync(ct);

                var monthlyExpenses = new List<decimal>();
                for (int month = 1; month <= 12; month++)
                {
                    var monthlyTotal = yearlyExpenses.Where(e => e.ExpenseDate.Month == month).Sum(e => e.Amount);
                    monthlyExpenses.Add(monthlyTotal);
                }

                result.MonthlyExpense = monthlyExpenses;
                return result;
            }
            catch (Exception)
            {
                return new MonthlyExpenseDto
                {
                    Year = year,
                    Months = new List<string> { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" },
                    MonthlyExpense = Enumerable.Repeat(0m, 12).ToList()
                };
            }
        }

        public async Task<ExpenseTotals> GetExpenseTotalsAsync(ExpensePagingParameters parameters, CancellationToken ct = default)
        {
            var companyId = _companyContext.GetCompanyId();
            try
            {
                IQueryable<Expense> query = _context.Expenses.AsNoTracking().Where(e => e.CompanyID == companyId && e.IsActive == true);

                if (parameters.StartDate.HasValue)
                {
                    query = query.Where(e => e.ExpenseDate >= parameters.StartDate.Value);
                }
                if (parameters.EndDate.HasValue)
                {
                    var endDate = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(e => e.ExpenseDate <= endDate);
                }
                if (!parameters.StartDate.HasValue && !parameters.EndDate.HasValue)
                {
                    var now = DateTime.Now;
                    var monthStart = new DateTime(now.Year, now.Month, 1);
                    var monthEnd = monthStart.AddMonths(1).AddTicks(-1);
                    query = query.Where(e => e.ExpenseDate >= monthStart && e.ExpenseDate <= monthEnd);
                }

                var expenses = await query.ToListAsync(ct);
                var result = new ExpenseTotals();

                var expensesByType = expenses
                    .Where(e => !string.IsNullOrEmpty(e.ExpenseType))
                    .GroupBy(e => e.ExpenseType!)
                    .ToDictionary(g => g.Key, g => g.Sum(e => e.Amount));

                result.Rent = expensesByType.Where(kvp => kvp.Key.ToLower().Contains("kira")).Sum(kvp => kvp.Value);
                result.Utilities = expensesByType.Where(kvp =>
                    kvp.Key.ToLower().Contains("elektrik") ||
                    kvp.Key.ToLower().Contains("su") ||
                    kvp.Key.ToLower().Contains("gaz") ||
                    kvp.Key.ToLower().Contains("internet") ||
                    kvp.Key.ToLower().Contains("telefon") ||
                    kvp.Key.ToLower().Contains("fatura")
                ).Sum(kvp => kvp.Value);
                result.Maintenance = expensesByType.Where(kvp =>
                    kvp.Key.ToLower().Contains("bakım") ||
                    kvp.Key.ToLower().Contains("onarım") ||
                    kvp.Key.ToLower().Contains("tamir")
                ).Sum(kvp => kvp.Value);
                result.Staff = expensesByType.Where(kvp =>
                    kvp.Key.ToLower().Contains("personel") ||
                    kvp.Key.ToLower().Contains("maaş") ||
                    kvp.Key.ToLower().Contains("ücret")
                ).Sum(kvp => kvp.Value);

                var categorizedAmount = result.Rent + result.Utilities + result.Maintenance + result.Staff;
                result.Other = expenses.Sum(e => e.Amount) - categorizedAmount;
                result.Total = expenses.Sum(e => e.Amount);
                result.ExpenseTypeBreakdown = expensesByType;

                return result;
            }
            catch (Exception)
            {
                return new ExpenseTotals();
            }
        }

        public async Task<Core.Utilities.Results.IResult> SoftDeleteExpenseAsync(int expenseId, int companyId, CancellationToken ct = default)
        {
            try
            {
                var expense = await _context.Expenses.FirstOrDefaultAsync(e => e.ExpenseID == expenseId && e.CompanyID == companyId, ct);
                if (expense == null)
                {
                    return new Core.Utilities.Results.ErrorResult("Gider bulunamadı.");
                }

                expense.IsActive = false;
                expense.DeletedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);
                return new Core.Utilities.Results.SuccessResult("Gider başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new Core.Utilities.Results.ErrorResult($"Gider silinirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<Core.Utilities.Results.IResult> AddExpenseWithBusinessLogicAsync(Expense expense, int companyId, CancellationToken ct = default)
        {
            try
            {
                if (expense.Amount <= 0)
                {
                    return new Core.Utilities.Results.ErrorResult("Gider tutarı pozitif olmalıdır.");
                }
                expense.CompanyID = companyId;
                expense.CreationDate = DateTime.Now;
                expense.IsActive = true;

                await _context.Expenses.AddAsync(expense, ct);
                await _context.SaveChangesAsync(ct);
                return new Core.Utilities.Results.SuccessResult("Gider başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new Core.Utilities.Results.ErrorResult($"Gider eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<Core.Utilities.Results.IResult> UpdateExpenseWithBusinessLogicAsync(Expense expense, int companyId, CancellationToken ct = default)
        {
            try
            {
                if (expense.Amount <= 0)
                {
                    return new Core.Utilities.Results.ErrorResult("Gider tutarı pozitif olmalıdır.");
                }
                var existingExpense = await _context.Expenses.FirstOrDefaultAsync(e => e.ExpenseID == expense.ExpenseID && e.CompanyID == companyId, ct);
                if (existingExpense == null)
                {
                    return new Core.Utilities.Results.ErrorResult("Gider bulunamadı.");
                }
                expense.CompanyID = companyId;
                expense.CreationDate = existingExpense.CreationDate;
                expense.UpdatedDate = DateTime.Now;

                _context.Entry(existingExpense).CurrentValues.SetValues(expense);
                await _context.SaveChangesAsync(ct);
                return new Core.Utilities.Results.SuccessResult("Gider başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new Core.Utilities.Results.ErrorResult($"Gider güncellenirken hata oluştu: {ex.Message}");
            }
        }


        private IQueryable<Expense> ApplyFilters(IQueryable<Expense> query, ExpensePagingParameters parameters)
        {
            // Arama metni filtresi
            if (!string.IsNullOrEmpty(parameters.SearchText))
            {
                var searchText = parameters.SearchText.ToLower();
                query = query.Where(e =>
                    (e.Description != null && e.Description.ToLower().Contains(searchText)) ||
                    (e.ExpenseType != null && e.ExpenseType.ToLower().Contains(searchText)));
            }

            // Tarih aralığı filtresi
            if (parameters.StartDate.HasValue)
            {
                query = query.Where(e => e.ExpenseDate >= parameters.StartDate.Value);
            }

            if (parameters.EndDate.HasValue)
            {
                var endDate = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                query = query.Where(e => e.ExpenseDate <= endDate);
            }

            // Gider türü filtresi
            if (!string.IsNullOrEmpty(parameters.ExpenseType))
            {
                query = query.Where(e => e.ExpenseType == parameters.ExpenseType);
            }

            // Tutar aralığı filtresi
            if (parameters.MinAmount.HasValue)
            {
                query = query.Where(e => e.Amount >= parameters.MinAmount.Value);
            }

            if (parameters.MaxAmount.HasValue)
            {
                query = query.Where(e => e.Amount <= parameters.MaxAmount.Value);
            }

            return query;
        }

        private IQueryable<Expense> ApplySorting(IQueryable<Expense> query, ExpensePagingParameters parameters)
        {
            if (string.IsNullOrEmpty(parameters.SortBy))
            {
                return query.OrderByDescending(e => e.ExpenseDate);
            }

            var isDescending = parameters.SortDirection?.ToLower() == "desc";

            return parameters.SortBy.ToLower() switch
            {
                "expensedate" => isDescending ? query.OrderByDescending(e => e.ExpenseDate) : query.OrderBy(e => e.ExpenseDate),
                "amount" => isDescending ? query.OrderByDescending(e => e.Amount) : query.OrderBy(e => e.Amount),
                "expensetype" => isDescending ? query.OrderByDescending(e => e.ExpenseType) : query.OrderBy(e => e.ExpenseType),
                "description" => isDescending ? query.OrderByDescending(e => e.Description) : query.OrderBy(e => e.Description),
                "creationdate" => isDescending ? query.OrderByDescending(e => e.CreationDate) : query.OrderBy(e => e.CreationDate),
                _ => query.OrderByDescending(e => e.ExpenseDate)
            };
        }



        public MonthlyExpenseDto GetMonthlyExpense(int year)
        {
            // DI kullanılıyor - Scalability optimized
            var companyId = _companyContext.GetCompanyId();

            try
            {
                if (year <= 0)
                {
                    year = DateTime.Now.Year;
                }

                var result = new MonthlyExpenseDto
                {
                    Year = year,
                    Months = new List<string> { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" }
                };

                // Yıllık giderleri al
                var yearlyExpenses = _context.Expenses
                    .Where(e => e.CompanyID == companyId && e.IsActive == true && e.ExpenseDate.Year == year)
                    .ToList();

                // Aylık toplamları hesapla
                var monthlyExpenses = new List<decimal>();
                for (int month = 1; month <= 12; month++)
                {
                    var monthlyTotal = yearlyExpenses
                        .Where(e => e.ExpenseDate.Month == month)
                        .Sum(e => e.Amount);
                    monthlyExpenses.Add(monthlyTotal);
                }

                result.MonthlyExpense = monthlyExpenses;
                return result;
            }
            catch (Exception)
            {
                // Hata durumunda boş veri döndür
                return new MonthlyExpenseDto
                {
                    Year = year,
                    Months = new List<string> { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" },
                    MonthlyExpense = Enumerable.Repeat(0m, 12).ToList()
                };
            }
        }

        public ExpenseTotals GetExpenseTotals(ExpensePagingParameters parameters)
        {
            // DI kullanılıyor - Scalability optimized
            var companyId = _companyContext.GetCompanyId();

            try
            {
                var query = _context.Expenses.Where(e => e.CompanyID == companyId && e.IsActive==true);

                // Tarih filtreleri
                if (parameters.StartDate.HasValue)
                {
                    query = query.Where(e => e.ExpenseDate >= parameters.StartDate.Value);
                }

                if (parameters.EndDate.HasValue)
                {
                    var endDate = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                    query = query.Where(e => e.ExpenseDate <= endDate);
                }

                // Eğer tarih filtresi yoksa, mevcut ayın verilerini al
                if (!parameters.StartDate.HasValue && !parameters.EndDate.HasValue)
                {
                    var now = DateTime.Now;
                    var monthStart = new DateTime(now.Year, now.Month, 1);
                    var monthEnd = monthStart.AddMonths(1).AddTicks(-1);
                    query = query.Where(e => e.ExpenseDate >= monthStart && e.ExpenseDate <= monthEnd);
                }

                var expenses = query.ToList();

                var result = new ExpenseTotals();

                // Dinamik gider türlerine göre toplamları hesapla
                var expensesByType = expenses
                    .Where(e => !string.IsNullOrEmpty(e.ExpenseType))
                    .GroupBy(e => e.ExpenseType!)
                    .ToDictionary(g => g.Key, g => g.Sum(e => e.Amount));

                // Sabit kategoriler için toplamları hesapla
                result.Rent = expensesByType.Where(kvp => kvp.Key.ToLower().Contains("kira")).Sum(kvp => kvp.Value);
                result.Utilities = expensesByType.Where(kvp =>
                    kvp.Key.ToLower().Contains("elektrik") ||
                    kvp.Key.ToLower().Contains("su") ||
                    kvp.Key.ToLower().Contains("gaz") ||
                    kvp.Key.ToLower().Contains("internet") ||
                    kvp.Key.ToLower().Contains("telefon") ||
                    kvp.Key.ToLower().Contains("fatura")
                ).Sum(kvp => kvp.Value);
                result.Maintenance = expensesByType.Where(kvp =>
                    kvp.Key.ToLower().Contains("bakım") ||
                    kvp.Key.ToLower().Contains("onarım") ||
                    kvp.Key.ToLower().Contains("tamir")
                ).Sum(kvp => kvp.Value);
                result.Staff = expensesByType.Where(kvp =>
                    kvp.Key.ToLower().Contains("personel") ||
                    kvp.Key.ToLower().Contains("maaş") ||
                    kvp.Key.ToLower().Contains("ücret")
                ).Sum(kvp => kvp.Value);

                // Diğer kategorilere girmeyen giderler
                var categorizedAmount = result.Rent + result.Utilities + result.Maintenance + result.Staff;
                result.Other = expenses.Sum(e => e.Amount) - categorizedAmount;

                result.Total = expenses.Sum(e => e.Amount);

                // Dinamik gider türleri bilgisini de ekle
                result.ExpenseTypeBreakdown = expensesByType;

                return result;
            }
            catch (Exception)
            {
                // Hata durumunda boş veri döndür
                return new ExpenseTotals();
            }
        }

        // SOLID prensiplerine uygun: Complex business operations DAL'da
    }
}