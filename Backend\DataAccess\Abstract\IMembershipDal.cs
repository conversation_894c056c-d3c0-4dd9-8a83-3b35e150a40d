﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IMembershipDal:IEntityRepository<Membership>
    {
        // Sync imzalar kaldırıldı - yalnızca async yüze<PERSON> kullanılacak

        // Async imzalar (read-only ve hesaplamalar)
        Task<List<Membership>> GetAllAsync(CancellationToken ct = default);
        Task<List<MembershipFreezeDto>> GetFrozenMembershipsAsync(CancellationToken ct = default);
        Task<List<MembershipDetailForDeleteDto>> GetMemberActiveMembershipsAsync(int memberId, CancellationToken ct = default);
        Task<IDataResult<LastMembershipInfoDto>> GetLastMembershipInfoWithCalculationsAsync(int memberId, int companyId, CancellationToken ct = default);

        // <PERSON> ka<PERSON>am async (write + helpers)
        Task<MembershipType> GetMembershipTypeAsync(int membershipTypeId, CancellationToken ct = default);
        Task FreezeMembershipAsync(int membershipId, int freezeDays, CancellationToken ct = default);
        Task UnfreezeMembershipAsync(int membershipId, CancellationToken ct = default);
        Task<bool> IsMembershipFrozenAsync(int membershipId, CancellationToken ct = default);
        Task<int> GetRemainingFreezeDaysAsync(int membershipId, CancellationToken ct = default);
        Task CancelFreezeAsync(int membershipId, CancellationToken ct = default);
        Task ReactivateFromTodayAsync(int membershipId, CancellationToken ct = default);

        Task<IResult> AddMembershipWithPaymentAndDebtAsync(MembershipAddDto membershipDto, CancellationToken ct = default);
        Task<IResult> DeleteMembershipWithRelatedDataAsync(int id, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateMembershipWithDateManagementAsync(MembershipUpdateDto membershipDto, int companyId, CancellationToken ct = default);
        Task<IResult> FreezeMembershipWithValidationAsync(MembershipFreezeRequestDto freezeRequest, int remainingDays, CancellationToken ct = default);
    }
}
