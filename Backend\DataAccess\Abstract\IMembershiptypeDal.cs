﻿using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IMembershiptypeDal:IEntityRepository<MembershipType>
    {
        // Sadece async yüzeyler
        Task<List<PackageWithCountDto>> GetPackagesByBranchAsync(string branch, CancellationToken ct = default);
        Task<PaginatedResult<MembershipType>> GetAllPaginatedAsync(MembershipTypePagingParameters parameters, CancellationToken ct = default);
        Task<List<BranchGetAllDto>> GetBranchesAndTypesAsync(CancellationToken ct = default);
        Task<IResult> SoftDeleteMembershipTypeAsync(int membershipTypeId, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateMembershipTypeWithBusinessLogicAsync(MembershipType membershipType, int companyId, CancellationToken ct = default);
    }
}
