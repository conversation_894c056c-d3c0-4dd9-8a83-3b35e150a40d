﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace DataAccess.Abstract
{
    public interface IDebtPaymentDal : IEntityRepository<DebtPayment>
    {
        Task<IResult> DeleteDebtPaymentWithRemainingDebtUpdateAsync(int debtPaymentId, CancellationToken ct = default);
    }
}
