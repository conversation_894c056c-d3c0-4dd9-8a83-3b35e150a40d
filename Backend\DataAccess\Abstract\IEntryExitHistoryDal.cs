﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace DataAccess.Abstract
{
    public interface IEntryExitHistoryDal: IEntityRepository<EntryExitHistory>
    {
        IResult AddWithCompanyId(EntryExitHistory entryExitHistory, int companyId);
        IResult UpdateWithCompanyId(EntryExitHistory entryExitHistory, int companyId);

        // Async imzalar (CT'li) - sync yüzeyler korunur
        Task<IResult> AddWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default);
    }
}
