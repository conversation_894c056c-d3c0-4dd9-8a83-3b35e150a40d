using Core.DataAccess;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace DataAccess.Abstract
{
    public interface ICompanyExerciseDal : IEntityRepository<CompanyExercise>
    {
        // Async imzalar (EF/IO yapan tüm metotlar için)
        Task<List<CompanyExerciseDto>> GetCompanyExercisesAsync(int companyId, CancellationToken ct = default);
        Task<List<CompanyExerciseDto>> GetCompanyExercisesByCategoryAsync(int companyId, int categoryId, CancellationToken ct = default);
        Task<PaginatedResult<CompanyExerciseDto>> GetCompanyExercisesFilteredAsync(int companyId, CompanyExerciseFilterDto filter, CancellationToken ct = default);
        Task<List<CompanyExerciseDto>> SearchCompanyExercisesAsync(int companyId, string searchTerm, CancellationToken ct = default);
        Task<CompanyExerciseDto> GetCompanyExerciseDetailAsync(int companyId, int exerciseId, CancellationToken ct = default);
        Task<List<CombinedExerciseDto>> GetCombinedExercisesAsync(int companyId, CancellationToken ct = default);
        Task<List<CombinedExerciseDto>> GetCombinedExercisesByCategoryAsync(int companyId, int categoryId, CancellationToken ct = default);
        Task<PaginatedResult<CombinedExerciseDto>> GetCombinedExercisesFilteredAsync(int companyId, SystemExerciseFilterDto filter, CancellationToken ct = default);

        Task<IResult> UpdateCompanyExerciseAsync(int companyId, CompanyExerciseUpdateDto exerciseUpdateDto, CancellationToken ct = default);
        Task<IResult> AddCompanyExerciseAsync(CompanyExerciseAddDto exerciseAddDto, int companyId, CancellationToken ct = default);
        Task<IResult> SoftDeleteCompanyExerciseAsync(int exerciseId, int companyId, CancellationToken ct = default);

    }
}
