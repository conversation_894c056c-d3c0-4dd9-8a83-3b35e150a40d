using Core.Utilities.Results;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Core.Entities.Concrete;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUnifiedCompanyDal : IUnifiedCompanyDal
    {
        private readonly GymContext _context;

        // Constructor injection (Scalability için)
        public EfUnifiedCompanyDal(GymContext context)
        {
            _context = context;
        }



        public async Task<IDataResult<string>> AddUnifiedCompanyWithAllEntitiesAsync(UnifiedCompanyAddDto unifiedCompanyDto, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();

                // 1. User oluştur (Adım 1'den gelen bilgiler)
                string tempPassword = "temp1234";
                if (!string.IsNullOrEmpty(unifiedCompanyDto.OwnerPhone) && unifiedCompanyDto.OwnerPhone.Length >= 4)
                {
                    tempPassword = unifiedCompanyDto.OwnerPhone.Substring(unifiedCompanyDto.OwnerPhone.Length - 4);
                }

                byte[] passwordHash, passwordSalt;
                HashingHelper.CreatePasswordHash(tempPassword, out passwordHash, out passwordSalt);

                var user = new User
                {
                    FirstName = unifiedCompanyDto.UserFirstName,
                    LastName = unifiedCompanyDto.UserLastName,
                    Email = unifiedCompanyDto.UserEmail,
                    PasswordHash = passwordHash,
                    PasswordSalt = passwordSalt,
                    IsActive = true,
                    RequirePasswordChange = true,
                    CreationDate = DateTime.Now
                };

                _context.Users.Add(user);
                await _context.SaveChangesAsync(ct);

                // 2. Company oluştur
                var company = new Company
                {
                    CompanyName = unifiedCompanyDto.CompanyName,
                    PhoneNumber = unifiedCompanyDto.CompanyPhone,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.Companies.Add(company);
                await _context.SaveChangesAsync(ct);

                // 3. CompanyAdress oluştur
                var companyAddress = new CompanyAdress
                {
                    CompanyID = company.CompanyID,
                    CityID = unifiedCompanyDto.CityID,
                    TownID = unifiedCompanyDto.TownID,
                    Adress = unifiedCompanyDto.Address,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.CompanyAdresses.Add(companyAddress);
                await _context.SaveChangesAsync(ct);

                // 4. CompanyUser oluştur
                var companyUser = new CompanyUser
                {
                    CityID = unifiedCompanyDto.CityID,
                    TownID = unifiedCompanyDto.TownID,
                    Name = unifiedCompanyDto.OwnerFullName,
                    PhoneNumber = unifiedCompanyDto.OwnerPhone,
                    Email = unifiedCompanyDto.UserEmail,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.CompanyUsers.Add(companyUser);
                await _context.SaveChangesAsync(ct);

                // 5. UserCompany ilişkisi
                var userCompany = new UserCompany
                {
                    UserID = companyUser.CompanyUserID,
                    CompanyId = company.CompanyID,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                _context.UserCompanies.Add(userCompany);
                await _context.SaveChangesAsync(ct);

                return new SuccessDataResult<string>(tempPassword, $"Salon başarıyla oluşturuldu. Geçici şifre: {tempPassword}");
            }
            catch (OperationCanceledException)
            {
                return new ErrorDataResult<string>("İşlem iptal edildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<string>("Salon oluşturulurken bir hata oluştu: " + ex.Message);
            }
        }
    }
}
