using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDal : EfEntityRepositoryBase<User, GymContext>, IUserDal
    {
        // Constructor injection (Scalability için)
        public EfUserDal(GymContext context) : base(context)
        {
        }



        public async Task<List<OperationClaim>> GetClaimsAsync(User user, CancellationToken ct = default)
        {
            var query = from OperationClaim in _context.OperationClaims.AsNoTracking()
                        join UserOperationClaim in _context.UserOperationClaims.AsNoTracking()
                        on OperationClaim.OperationClaimId equals UserOperationClaim.OperationClaimId
                        where UserOperationClaim.UserId == user.UserID
                        select new OperationClaim { OperationClaimId = OperationClaim.OperationClaimId, Name = OperationClaim.Name };
            return await query.ToListAsync(ct);
        }



        public async Task<List<User>> GetNonMembersAsync(CancellationToken ct = default)
        {
            var memberRoleId = await _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefaultAsync(ct);

            IQueryable<User> query = _context.Users.AsNoTracking().Where(u => u.IsActive);

            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);
                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            return await query
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync(ct);
        }



        public async Task<List<User>> GetNonMembersPaginatedAsync(int page, int pageSize, string searchTerm, CancellationToken ct = default)
        {
            var memberRoleId = await _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefaultAsync(ct);

            IQueryable<User> query = _context.Users.AsNoTracking().Where(u => u.IsActive);

            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            return await query
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(ct);
        }

        /// <summary>
        /// Member rolü olmayan kullanıcı sayısını getirir
        /// </summary>


        public async Task<int> GetNonMembersCountAsync(string searchTerm, CancellationToken ct = default)
        {
            var memberRoleId = await _context.OperationClaims
                .Where(oc => oc.Name == "member" && oc.IsActive == true)
                .Select(oc => oc.OperationClaimId)
                .FirstOrDefaultAsync(ct);

            IQueryable<User> query = _context.Users.AsNoTracking().Where(u => u.IsActive);

            if (memberRoleId > 0)
            {
                var usersWithMemberRole = _context.UserOperationClaims
                    .Where(uoc => uoc.OperationClaimId == memberRoleId && uoc.IsActive == true)
                    .Select(uoc => uoc.UserId);

                query = query.Where(u => !usersWithMemberRole.Contains(u.UserID));
            }

            if (!string.IsNullOrEmpty(searchTerm))
            {
                var lowerSearchTerm = searchTerm.ToLower();
                query = query.Where(u =>
                    u.FirstName.ToLower().Contains(lowerSearchTerm) ||
                    u.LastName.ToLower().Contains(lowerSearchTerm) ||
                    u.Email.ToLower().Contains(lowerSearchTerm));
            }

            return await query.CountAsync(ct);
        }

        /// <summary>
        /// User tablosu güncellendiğinde CompanyUser tablosundaki ilgili kayıtları senkronize eder
        /// SOLID prensiplerine uygun: Complex database operations DAL katmanında
        /// </summary>


        public async Task<IResult> SyncCompanyUserDataAsync(User updatedUser, User oldUser, CancellationToken ct = default)
        {
            try
            {
                var companyUsers = await _context.CompanyUsers
                    .Where(cu => cu.Email == oldUser.Email && cu.IsActive == true)
                    .ToListAsync(ct);

                string newEmail = updatedUser.Email;
                string newFullName = $"{updatedUser.FirstName} {updatedUser.LastName}".Trim();

                foreach (var companyUser in companyUsers)
                {
                    bool emailChanged = companyUser.Email != newEmail;
                    bool nameChanged = companyUser.Name != newFullName;

                    if (emailChanged || nameChanged)
                    {
                        string updateSql = @"
                            UPDATE CompanyUsers
                            SET Email = @Email,
                                Name = @Name,
                                UpdatedDate = @UpdatedDate
                            WHERE CompanyUserID = @CompanyUserID";

                        var sqlParams = new object[]
                        {
                            new Microsoft.Data.SqlClient.SqlParameter("@Email", newEmail),
                            new Microsoft.Data.SqlClient.SqlParameter("@Name", newFullName),
                            new Microsoft.Data.SqlClient.SqlParameter("@UpdatedDate", DateTime.Now),
                            new Microsoft.Data.SqlClient.SqlParameter("@CompanyUserID", companyUser.CompanyUserID)
                        };

                        await _context.Database.ExecuteSqlRawAsync(updateSql, sqlParams, ct);
                    }
                }

                return new SuccessResult("CompanyUser verileri başarıyla senkronize edildi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"CompanyUser senkronizasyonu sırasında hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Profile image path güncelleme işlemi DAL katmanında
        /// </summary>


        public async Task<IResult> UpdateProfileImagePathAsync(int userId, string imagePath, CancellationToken ct = default)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.UserID == userId && u.IsActive, ct);
                if (user == null)
                {
                    return new ErrorResult("Kullanıcı bulunamadı.");
                }

                user.ProfileImagePath = imagePath;
                user.UpdatedDate = DateTime.Now;
                _context.Users.Update(user);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Profil fotoğrafı yolu başarıyla güncellendi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil fotoğrafı yolu güncellenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Profile image path temizleme işlemi DAL katmanında
        /// </summary>


        public async Task<IResult> ClearProfileImagePathAsync(int userId, CancellationToken ct = default)
        {
            try
            {
                var user = await _context.Users.FirstOrDefaultAsync(u => u.UserID == userId && u.IsActive, ct);
                if (user == null)
                {
                    return new ErrorResult("Kullanıcı bulunamadı.");
                }

                user.ProfileImagePath = null;
                user.UpdatedDate = DateTime.Now;
                _context.Users.Update(user);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Profil fotoğrafı yolu başarıyla temizlendi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil fotoğrafı yolu temizlenirken hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// SOLID prensiplerine uygun: Validation logic DAL katmanda
        /// </summary>


        public async Task<IDataResult<User>> GetUserByIdWithValidationAsync(int userId, CancellationToken ct = default)
        {
            try
            {
                var user = await _context.Users.AsNoTracking().FirstOrDefaultAsync(u => u.UserID == userId && u.IsActive, ct);
                if (user == null)
                {
                    return new ErrorDataResult<User>("Kullanıcı bulunamadı");
                }
                return new SuccessDataResult<User>(user);
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<User>($"Kullanıcı getirilirken hata oluştu: {ex.Message}");
            }
        }

    }
}
