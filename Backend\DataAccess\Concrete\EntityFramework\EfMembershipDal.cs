﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Transactions;
using Microsoft.EntityFrameworkCore;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMembershipDal : EfCompanyEntityRepositoryBase<Membership, GymContext>, IMembershipDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;
        private readonly IPaymentDal _paymentDal;
        private readonly IRemainingDebtDal _remainingDebtDal;
        private readonly IDebtPaymentDal _debtPaymentDal;


        // ASYNC imzalar
        public async Task<List<Membership>> GetAllAsync(CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            if (companyId <= 0) return new List<Membership>();

            return await _context.Memberships
                .AsNoTracking()
                .Where(m => m.CompanyID == companyId)
                .OrderBy(m => m.MembershipID)
                .ToListAsync(ct);
        }

        public async Task<List<MembershipFreezeDto>> GetFrozenMembershipsAsync(CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var frozenMemberships = from m in _context.Memberships.AsNoTracking()
                                    join mem in _context.Members.AsNoTracking() on m.MemberID equals mem.MemberID
                                    join mt in _context.MembershipTypes.AsNoTracking() on m.MembershipTypeID equals mt.MembershipTypeID
                                    where m.IsFrozen && m.IsActive == true
                                          && mem.IsActive == true
                                          && m.CompanyID == companyId
                                          && mem.CompanyID == companyId
                                          && mt.CompanyID == companyId
                                    select new MembershipFreezeDto
                                    {
                                        MembershipID = m.MembershipID,
                                        MemberName = mem.Name,
                                        PhoneNumber = mem.PhoneNumber,
                                        StartDate = m.StartDate,
                                        EndDate = m.EndDate,
                                        FreezeStartDate = m.FreezeStartDate.Value,
                                        FreezeEndDate = m.FreezeEndDate.Value,
                                        FreezeDays = (int)m.FreezeDays,
                                        Branch = mt.Branch
                                    };
            return await frozenMemberships
                .OrderBy(x => x.MembershipID)
                .ToListAsync(ct);
        }

        public async Task<List<MembershipDetailForDeleteDto>> GetMemberActiveMembershipsAsync(int memberId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var now = DateTime.Now;

            var activeMemberships = from m in _context.Memberships.AsNoTracking()
                                   join mt in _context.MembershipTypes.AsNoTracking() on m.MembershipTypeID equals mt.MembershipTypeID
                                   where m.MemberID == memberId
                                   && m.IsActive == true
                                   && m.EndDate > now
                                   && m.CompanyID == companyId
                                   && mt.CompanyID == companyId
                                   select new MembershipDetailForDeleteDto
                                   {
                                       MembershipID = m.MembershipID,
                                       MembershipTypeID = m.MembershipTypeID,
                                       Branch = mt.Branch,
                                       PackageName = mt.TypeName,
                                       RemainingDays = (int)Math.Ceiling((m.EndDate - now).TotalDays),
                                       StartDate = m.StartDate,
                                       EndDate = m.EndDate,
                                       IsActive = m.IsActive == true,
                                       IsFrozen = m.IsFrozen
                                   };

            return await activeMemberships
                .OrderBy(x => x.MembershipID)
                .ToListAsync(ct);
        }

        public async Task<IDataResult<LastMembershipInfoDto>> GetLastMembershipInfoWithCalculationsAsync(int memberId, int companyId, CancellationToken ct = default)
        {
            try
            {
                var lastMembership = await _context.Memberships.AsNoTracking()
                    .Where(m => m.MemberID == memberId && m.CompanyID == companyId && m.DeletedDate == null)
                    .OrderByDescending(m => m.EndDate)
                    .FirstOrDefaultAsync(ct);

                if (lastMembership != null)
                {
                    var now = DateTime.Now;
                    var daysRemaining = Math.Ceiling((lastMembership.EndDate - now).TotalDays);
                    var isActive = daysRemaining > 0;

                    return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
                    {
                        LastEndDate = lastMembership.EndDate,
                        DaysRemaining = (int)daysRemaining,
                        IsActive = isActive
                    });
                }

                return new SuccessDataResult<LastMembershipInfoDto>(new LastMembershipInfoDto
                {
                    LastEndDate = null,
                    DaysRemaining = 0,
                    IsActive = false
                });
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<LastMembershipInfoDto>($"Son üyelik bilgisi alınırken hata oluştu: {ex.Message}");
            }
        }

        public async Task<MembershipType> GetMembershipTypeAsync(int membershipTypeId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            return await _context.MembershipTypes
                .AsNoTracking()
                .SingleOrDefaultAsync(mt => mt.MembershipTypeID == membershipTypeId && mt.CompanyID == companyId, ct);
        }

        public async Task<bool> IsMembershipFrozenAsync(int membershipId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            return await _context.Memberships
                .AsNoTracking()
                .Where(m => m.MembershipID == membershipId && m.CompanyID == companyId)
                .Select(m => m.IsFrozen == true)
                .FirstOrDefaultAsync(ct);
        }

        public async Task<int> GetRemainingFreezeDaysAsync(int membershipId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var membership = await _context.Memberships
                .AsNoTracking()
                .SingleOrDefaultAsync(m => m.MembershipID == membershipId && m.CompanyID == companyId, ct);
            if (membership?.IsFrozen == true && membership.FreezeEndDate.HasValue)
            {
                var remainingDays = (membership.FreezeEndDate.Value.Date - DateTime.Now.Date).Days;
                return remainingDays > 0 ? remainingDays : 0;
            }
            return 0;
        }

        public async Task FreezeMembershipAsync(int membershipId, int freezeDays, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var membership = await _context.Memberships
                .SingleOrDefaultAsync(m => m.MembershipID == membershipId && m.CompanyID == companyId, ct);
            if (membership != null)
            {
                membership.IsFrozen = true;
                membership.FreezeStartDate = DateTime.Now;
                membership.FreezeEndDate = DateTime.Now.AddDays(freezeDays).Date.AddHours(0).AddMinutes(1);
                membership.FreezeDays = freezeDays;
                membership.OriginalEndDate = membership.EndDate;
                membership.EndDate = membership.EndDate.AddDays(freezeDays);
                membership.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);
            }
        }

        public async Task UnfreezeMembershipAsync(int membershipId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var membership = await _context.Memberships
                .SingleOrDefaultAsync(m => m.MembershipID == membershipId && m.CompanyID == companyId, ct);
            if (membership != null)
            {
                var remainingFreezeDays = (membership.FreezeEndDate?.Date - DateTime.Now.Date)?.Days ?? 0;
                if (remainingFreezeDays < 0) remainingFreezeDays = 0;

                membership.IsFrozen = false;
                membership.EndDate = membership.EndDate.AddDays(-remainingFreezeDays);
                membership.FreezeStartDate = null;
                membership.FreezeEndDate = null;
                membership.FreezeDays = 0;
                membership.OriginalEndDate = null;
                membership.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);
            }
        }

        public async Task CancelFreezeAsync(int membershipId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var membership = await _context.Memberships
                .SingleOrDefaultAsync(m => m.MembershipID == membershipId && m.CompanyID == companyId, ct);
            if (membership != null && membership.IsFrozen)
            {
                membership.IsFrozen = false;
                membership.EndDate = membership.OriginalEndDate ?? membership.EndDate;
                membership.FreezeStartDate = null;
                membership.FreezeEndDate = null;
                membership.FreezeDays = 0;
                membership.OriginalEndDate = null;
                membership.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);
            }
        }

        public async Task ReactivateFromTodayAsync(int membershipId, CancellationToken ct = default)
        {
            int companyId = _companyContext.GetCompanyId();
            var membership = await _context.Memberships
                .SingleOrDefaultAsync(m => m.MembershipID == membershipId && m.CompanyID == companyId, ct);
            if (membership != null && membership.IsFrozen)
            {
                var today = DateTime.Now.Date;
                var usedFreezeDays = membership.FreezeStartDate.HasValue ? (today - membership.FreezeStartDate.Value.Date).Days : 0;

                membership.IsFrozen = false;
                membership.EndDate = membership.OriginalEndDate?.AddDays(usedFreezeDays) ?? membership.EndDate;
                membership.FreezeStartDate = null;
                membership.FreezeEndDate = null;
                membership.FreezeDays = 0;
                membership.OriginalEndDate = null;
                membership.UpdatedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);
            }
        }

        public async Task<IResult> AddMembershipWithPaymentAndDebtAsync(MembershipAddDto membershipDto, CancellationToken ct = default)
        {
            try
            {
                using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
                {
                    // Aynı paket türü varsa uzatma, yoksa yeni satır
                    var existingMembership = await GetAsync(m =>
                        m.MemberID == membershipDto.MemberID &&
                        m.MembershipTypeID == membershipDto.MembershipTypeID &&
                        m.EndDate >= DateTime.Now &&
                        m.IsActive == true, ct);

                    string paymentStatus = membershipDto.PaymentMethod == "Borç" ? "Pending" : "Completed";

                    if (existingMembership != null)
                    {
                        existingMembership.EndDate = existingMembership.EndDate.AddDays(membershipDto.Day);
                        existingMembership.UpdatedDate = DateTime.Now;
                        await UpdateAsync(existingMembership, ct);

                        var payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,
                            FinalPaymentMethod = membershipDto.PaymentMethod,
                            PaymentDate = DateTime.Now,
                            MemberShipID = existingMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        await _paymentDal.AddAsync(payment, ct);

                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            var remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            await _remainingDebtDal.AddAsync(remainingDebt, ct);
                        }

                        scope.Complete();
                        return new SuccessResult("Üyelik başarıyla uzatıldı");
                    }
                    else
                    {
                        var newMembership = new Membership
                        {
                            MemberID = membershipDto.MemberID,
                            MembershipTypeID = membershipDto.MembershipTypeID,
                            StartDate = membershipDto.StartDate,
                            EndDate = membershipDto.EndDate,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        await AddAsync(newMembership, ct);

                        var payment = new Payment
                        {
                            PaymentAmount = membershipDto.Price,
                            PaymentMethod = membershipDto.PaymentMethod,
                            OriginalPaymentMethod = membershipDto.PaymentMethod,
                            FinalPaymentMethod = membershipDto.PaymentMethod,
                            PaymentDate = DateTime.Now,
                            MemberShipID = newMembership.MembershipID,
                            PaymentStatus = paymentStatus,
                            CreationDate = DateTime.Now,
                            IsActive = true
                        };
                        await _paymentDal.AddAsync(payment, ct);

                        if (membershipDto.PaymentMethod == "Borç")
                        {
                            var remainingDebt = new RemainingDebt
                            {
                                PaymentID = payment.PaymentID,
                                OriginalAmount = membershipDto.Price,
                                RemainingAmount = membershipDto.Price,
                                LastUpdateDate = DateTime.Now,
                                IsActive = true,
                                CreationDate = DateTime.Now
                            };
                            await _remainingDebtDal.AddAsync(remainingDebt, ct);
                        }

                        scope.Complete();
                        return new SuccessResult("Üyelik başarıyla eklendi");
                    }
                }
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik işlemi sırasında hata oluştu: {ex.Message}");
            }
        }



        // Constructor injection (Scalability için)
        public EfMembershipDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context, IPaymentDal paymentDal, IRemainingDebtDal remainingDebtDal, IDebtPaymentDal debtPaymentDal) : base(companyContext, context)
        {
            _companyContext = companyContext;
            _paymentDal = paymentDal;
            _remainingDebtDal = remainingDebtDal;
            _debtPaymentDal = debtPaymentDal;
        }
























        public async Task<IResult> DeleteMembershipWithRelatedDataAsync(int id, int companyId, CancellationToken ct = default)
        {
            using (var scope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled))
            {
                try
                {
                    var membership = await GetAsync(m => m.MembershipID == id && m.CompanyID == companyId, ct);
                    if (membership == null)
                    {
                        return new ErrorResult("Üyelik bulunamadı veya erişim yetkiniz yok.");
                    }

                    var payments = await _context.Payments.Where(p => p.MemberShipID == id && p.CompanyID == companyId).ToListAsync(ct);

                    var paymentIds = payments.Select(p => p.PaymentID).ToList();
                    var remainingDebts = await _context.RemainingDebts
                        .Where(rd => paymentIds.Contains(rd.PaymentID) && rd.CompanyID == companyId)
                        .ToListAsync(ct);

                    var remainingDebtIds = remainingDebts.Select(rd => rd.RemainingDebtID).ToList();
                    var allDebtPayments = remainingDebtIds.Count > 0
                        ? await _context.DebtPayments
                            .Where(dp => remainingDebtIds.Contains(dp.RemainingDebtID) && dp.CompanyID == companyId)
                            .ToListAsync(ct)
                        : new List<DebtPayment>();

                    // DebtPayments pasifleştirme
                    foreach (var dp in allDebtPayments)
                    {
                        dp.IsActive = false;
                        _context.DebtPayments.Update(dp);
                    }

                    // RemainingDebts pasifleştirme
                    foreach (var rd in remainingDebts)
                    {
                        rd.IsActive = false;
                        _context.RemainingDebts.Update(rd);
                    }

                    // Payments pasifleştirme
                    foreach (var payment in payments)
                    {
                        payment.IsActive = false;
                        payment.DeletedDate = DateTime.Now;
                        _context.Payments.Update(payment);
                    }

                    await DeleteAsync(membership.MembershipID, ct);

                    await _context.SaveChangesAsync(ct);
                    scope.Complete();
                    return new SuccessResult("Üyelik başarıyla silindi");
                }
                catch (Exception ex)
                {
                    return new ErrorResult($"Üyelik silinirken bir hata oluştu: {ex.Message}");
                }
            }
        }

        public async Task<IResult> UpdateMembershipWithDateManagementAsync(MembershipUpdateDto membershipDto, int companyId, CancellationToken ct = default)
        {
            try
            {
                var membership = await GetAsync(m => m.MembershipID == membershipDto.MembershipID && m.CompanyID == companyId, ct);

                if (membership == null)
                {
                    return new ErrorResult("Üyelik bulunamadı veya erişim yetkiniz yok.");
                }

                membership.MembershipTypeID = membershipDto.MembershipTypeID;
                membership.StartDate = membershipDto.StartDate;
                membership.EndDate = membershipDto.EndDate;
                membership.UpdatedDate = DateTime.Now;

                await UpdateAsync(membership, ct);
                return new SuccessResult("Üyelik başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> FreezeMembershipWithValidationAsync(MembershipFreezeRequestDto freezeRequest, int remainingDays, CancellationToken ct = default)
        {
            try
            {
                int companyId = _companyContext.GetCompanyId();

                if (freezeRequest.FreezeDays < 1)
                    return new ErrorResult("Dondurma günü en az 1 olmalıdır");

                if (await IsMembershipFrozenAsync(freezeRequest.MembershipID, ct))
                    return new ErrorResult("Üyelik zaten dondurulmuş durumda");

                var freezeStartDate = DateTime.Now;
                var freezeEndDate = freezeStartDate.AddDays(freezeRequest.FreezeDays)
                    .Date
                    .AddHours(0)
                    .AddMinutes(1);

                await FreezeMembershipAsync(freezeRequest.MembershipID, freezeRequest.FreezeDays, ct);

                var freezeHistory = new MembershipFreezeHistory
                {
                    MembershipID = freezeRequest.MembershipID,
                    CompanyID = companyId,
                    StartDate = freezeStartDate,
                    PlannedEndDate = freezeEndDate,
                    FreezeDays = freezeRequest.FreezeDays,
                    CreationDate = DateTime.Now
                };
                await _context.MembershipFreezeHistory.AddAsync(freezeHistory, ct);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Üyelik başarıyla donduruldu");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Üyelik dondurulurken hata oluştu: {ex.Message}");
            }
        }





        /// <summary>
        /// SOLID prensiplerine uygun: Freeze business logic DAL katmanında
        /// </summary>

    }
}
