﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfEntryExitHistoryDal : EfCompanyEntityRepositoryBase<EntryExitHistory, GymContext>, IEntryExitHistoryDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfEntryExitHistoryDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }

        public IResult AddWithCompanyId(EntryExitHistory entryExitHistory, int companyId)
        {
            try
            {
                // CompanyID'yi ayarla
                entryExitHistory.CompanyID = companyId;

                // DI kullanılıyor - Scalability optimized
                _context.EntryExitHistories.Add(entryExitHistory);
                _context.SaveChanges();

                return new SuccessResult("Giriş-çıkış kaydı başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Giriş-çıkış kaydı eklenirken hata oluştu: {ex.Message}");
            }
        }

        public IResult UpdateWithCompanyId(EntryExitHistory entryExitHistory, int companyId)
        {
            try
            {
                // CompanyID'yi ayarla
                entryExitHistory.CompanyID = companyId;

                // DI kullanılıyor - Scalability optimized
                _context.EntryExitHistories.Update(entryExitHistory);
                _context.SaveChanges();

                return new SuccessResult("Giriş-çıkış kaydı başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Giriş-çıkış kaydı güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> AddWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                entryExitHistory.CompanyID = companyId;
                await _context.EntryExitHistories.AddAsync(entryExitHistory, ct);
                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Giriş-çıkış kaydı başarıyla eklendi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Giriş-çıkış kaydı eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateWithCompanyIdAsync(EntryExitHistory entryExitHistory, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                entryExitHistory.CompanyID = companyId;
                _context.EntryExitHistories.Update(entryExitHistory);
                await _context.SaveChangesAsync(ct);
                return new SuccessResult("Giriş-çıkış kaydı başarıyla güncellendi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Giriş-çıkış kaydı güncellenirken hata oluştu: {ex.Message}");
            }
        }
    }
}
