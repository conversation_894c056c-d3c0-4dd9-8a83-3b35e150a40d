﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfOperationClaimDal : EfEntityRepositoryBase<OperationClaim, GymContext>, IOperationClaimDal
    {
        // Constructor injection (Scalability için)
        public EfOperationClaimDal(GymContext context) : base(context)
        {
        }


        public async Task<IDataResult<OperationClaim>> GetOperationClaimByNameWithValidationAsync(string name, CancellationToken ct = default)
        {
            try
            {
                if (name != "member")
                {
                    return new ErrorDataResult<OperationClaim>(null, "Bu metot sadece 'member' rolü için kull<PERSON>ılabil<PERSON>");
                }

                var operationClaim = await _context.OperationClaims
                    .AsNoTracking()
                    .FirstOrDefaultAsync(o => o.Name == name, ct);

                var ipAddress = System.Net.Dns.GetHostName();
                var timestamp = DateTime.Now;
                Console.WriteLine($"[{timestamp}] GetByNameAsync called for role '{name}', IP: {ipAddress}");

                return new SuccessDataResult<OperationClaim>(operationClaim);
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<OperationClaim>(null, $"OperationClaim alınırken hata oluştu (async): {ex.Message}");
            }
        }
    }

}
