﻿using Core.DataAccess;
using Core.DataAccess.EntityFramework;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ICompanyAdressDal:IEntityRepository<CompanyAdress>
    {
        Task<List<CompanyAdressDetailDto>> GetCompanyAdressDetailsAsync(CancellationToken ct = default);

    }
}
