﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ILicenseTransactionDal : IEntityRepository<LicenseTransaction>
    {
        // Async imzalar (CancellationToken ile)
        Task<IDataResult<List<LicenseTransaction>>> GetAllFilteredAsync(int? userID, string startDate, string endDate, int page, int pageSize, CancellationToken ct = default);
        Task<IResult> AddLicenseTransactionAsync(LicenseTransaction licenseTransaction, CancellationToken ct = default);
        Task<IResult> SoftDeleteLicenseTransactionAsync(int id, CancellationToken ct = default);
        Task<IDataResult<List<LicenseTransaction>>> GetAllOrderedByDateAsync(CancellationToken ct = default);
        Task<IDataResult<object>> GetTotalAmountsByPaymentMethodAsync(CancellationToken ct = default);
        Task<IDataResult<object>> GetMonthlyRevenueByYearAsync(int year, CancellationToken ct = default);
    }

}
