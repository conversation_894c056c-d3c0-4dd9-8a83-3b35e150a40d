using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Entities.Concrete;
using Core.Utilities.Security.Hashing;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Cryptography;
using System.Text;
using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberDal : EfCompanyEntityRepositoryBase<Member, GymContext>, IMemberDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfMemberDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }




        // Doğum gününe kalan gün sayısını hesaplayan yardımcı metod
        private int CalculateDaysUntilBirthday(DateOnly? birthDate, DateTime today)
        {
            if (!birthDate.HasValue) return int.MaxValue; // Doğum tarihi yoksa en sona koy

            var birth = birthDate.Value;

            // Bu yılki doğum günü
            var birthThisYear = new DateTime(today.Year, birth.Month, birth.Day);

            // Eğer bu yılki doğum günü geçtiyse, gelecek yılki doğum gününü hesapla
            if (birthThisYear < today)
            {
                birthThisYear = birthThisYear.AddYears(1);
            }

            // Doğum gününe kalan gün sayısı
            var daysUntil = (int)(birthThisYear - today).TotalDays;

            return daysUntil;
        }












        public async Task<List<MemberEntryDto>> GetTodayEntriesAsync(DateTime date, CancellationToken ct = default)
        {
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;

                var baseQuery = from m in _context.Members
                                join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                join eh in _context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                                where eh.EntryDate.HasValue && eh.EntryDate.Value.Date == date.Date
                                      && m.IsActive == true
                                      && ms.IsActive == true
                                      && m.CompanyID == companyId
                                      && ms.CompanyID == companyId
                                      && eh.CompanyID == companyId
                                select new
                                {
                                    MemberID = m.MemberID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    EntryTime = eh.EntryDate.Value,
                                    ExitTime = eh.ExitDate,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate
                                };

                var baseList = await baseQuery.AsNoTracking().ToListAsync(ct);

                // Batch: İlgili üyelerin aktif üyeliklerini tek seferde çek ve RemainingDays sözlüğünü oluştur
                var memberIds = baseList.Select(x => x.MemberID).Distinct().ToList();
                var memberships = await _context.Memberships
                    .AsNoTracking()
                    .Where(ms => ms.IsActive == true && ms.CompanyID == companyId && memberIds.Contains(ms.MemberID))
                    .ToListAsync(ct);

                var remainingDaysByMember = memberships
                    .Where(ms => ms.EndDate > now)
                    .GroupBy(ms => ms.MemberID)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(ms => ms.StartDate > now ? 0 : (int)Math.Ceiling((ms.EndDate - now).TotalDays))
                    );

                var result = baseList
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.EntryTime, x.ExitTime })
                    .Select(g => new MemberEntryDto
                    {
                        MemberID = g.Key.MemberID,
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        EntryTime = g.Key.EntryTime,
                        ExitTime = g.Key.ExitTime,
                        RemainingDays = remainingDaysByMember.TryGetValue(g.Key.MemberID, out var days) ? days : 0
                    })
                    .OrderByDescending(x => x.EntryTime)
                    .ToList();

                return result;
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<PaginatedResult<MemberEntryDto>> GetTodayEntriesPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default)
        {
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                var targetDate = parameters.Date ?? DateTime.Today;

                var baseQuery = from m in _context.Members
                                join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                join eh in _context.EntryExitHistories on ms.MembershipID equals eh.MembershipID
                                where eh.EntryDate.HasValue && eh.EntryDate.Value.Date == targetDate.Date
                                      && m.IsActive == true
                                      && ms.IsActive == true
                                      && m.CompanyID == companyId
                                      && ms.CompanyID == companyId
                                      && eh.CompanyID == companyId
                                select new
                                {
                                    MemberID = m.MemberID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    EntryTime = eh.EntryDate.Value,
                                    ExitTime = eh.ExitDate,
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate
                                };

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    baseQuery = baseQuery.Where(x => x.Name.Contains(parameters.SearchText) || x.PhoneNumber.Contains(parameters.SearchText));
                }

                var baseList = await baseQuery.AsNoTracking().ToListAsync(ct);

                // Batch: İlgili üyelerin aktif üyeliklerini tek seferde çek ve RemainingDays sözlüğünü oluştur
                var memberIds = baseList.Select(x => x.MemberID).Distinct().ToList();
                var memberships = await _context.Memberships
                    .AsNoTracking()
                    .Where(ms => ms.IsActive == true && ms.CompanyID == companyId && memberIds.Contains(ms.MemberID))
                    .ToListAsync(ct);

                var remainingDaysByMember = memberships
                    .Where(ms => ms.EndDate > now)
                    .GroupBy(ms => ms.MemberID)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Sum(ms => ms.StartDate > now ? 0 : (int)Math.Ceiling((ms.EndDate - now).TotalDays))
                    );

                var groupedList = baseList
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.EntryTime, x.ExitTime })
                    .Select(g => new MemberEntryDto
                    {
                        MemberID = g.Key.MemberID,
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        EntryTime = g.Key.EntryTime,
                        ExitTime = g.Key.ExitTime,
                        RemainingDays = remainingDaysByMember.TryGetValue(g.Key.MemberID, out var days) ? days : 0
                    })
                    .OrderByDescending(x => x.EntryTime)
                    .ToList();

                var totalCount = groupedList.Count;
                var items = groupedList
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return new PaginatedResult<MemberEntryDto>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }





        // Prompt 4 — Async (CT'li)
        public async Task<List<MembeFilterDto>> GetMemberDetailsAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                var rows = await (from m in _context.Members.AsNoTracking()
                                  join s in _context.Memberships.AsNoTracking() on m.MemberID equals s.MemberID
                                  join x in _context.MembershipTypes.AsNoTracking() on s.MembershipTypeID equals x.MembershipTypeID
                                  where m.IsActive == true && s.IsActive == true &&
                                        m.CompanyID == companyId && s.CompanyID == companyId && x.CompanyID == companyId
                                  select new
                                  {
                                      m.MemberID,
                                      s.MembershipID,
                                      x.MembershipTypeID,
                                      m.Name,
                                      m.Gender,
                                      m.PhoneNumber,
                                      x.TypeName,
                                      x.Branch,
                                      s.StartDate,
                                      s.EndDate
                                  }).ToListAsync(ct);

                var grouped = rows
                    .GroupBy(r => new { r.MemberID, r.Name, r.Gender, r.PhoneNumber, r.Branch })
                    .Select(g => new MembeFilterDto
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(r => r.MembershipID),
                        MembershipTypeID = g.Max(r => r.MembershipTypeID),
                        Name = g.Key.Name,
                        Gender = g.Key.Gender,
                        PhoneNumber = g.Key.PhoneNumber,
                        Branch = g.Key.Branch,
                        TypeName = string.Join(", ", g.Select(r => r.TypeName).Distinct()),
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r => r.EndDate > now ? (int)Math.Ceiling((r.EndDate - now).TotalDays) : 0),
                        IsActive = true
                    })
                    .OrderBy(x => x.Name)
                    .ToList();
                return grouped;
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<List<MemberEntryExitHistoryDto>> GetMemberEntryExitHistoryAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var currentTime = DateTime.Now;
                var result = await (from a in _context.Members.AsNoTracking()
                                    join x in _context.Memberships.AsNoTracking() on a.MemberID equals x.MemberID
                                    join s in _context.EntryExitHistories.AsNoTracking() on x.MembershipID equals s.MembershipID
                                    where a.IsActive == true && s.IsActive == true && s.EntryDate.HasValue &&
                                          EF.Functions.DateDiffMinute(s.EntryDate.Value, currentTime) <= 300 &&
                                          a.CompanyID == companyId && x.CompanyID == companyId && s.CompanyID == companyId
                                    select new MemberEntryExitHistoryDto
                                    {
                                        MemberID = a.MemberID,
                                        MemberName = a.Name,
                                        EntryDate = s.EntryDate,
                                        IsActive = s.IsActive,
                                    })
                                   .OrderByDescending(d => d.EntryDate)
                                   .ToListAsync(ct);
                return result;
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<List<MemberRemainingDayDto>> GetMemberRemainingDayAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var now = DateTime.Now;
                var baseRows = await (from a in _context.Members.AsNoTracking()
                                      join b in _context.Memberships.AsNoTracking() on a.MemberID equals b.MemberID
                                      join c in _context.MembershipTypes.AsNoTracking() on b.MembershipTypeID equals c.MembershipTypeID
                                      where a.IsActive == true && b.IsActive == true &&
                                            a.CompanyID == companyId && b.CompanyID == companyId && c.CompanyID == companyId
                                      select new
                                      {
                                          a.MemberID,
                                          a.Name,
                                          a.PhoneNumber,
                                          b.StartDate,
                                          b.EndDate,
                                          c.Branch
                                      }).ToListAsync(ct);

                var projected = baseRows.Select(x => new
                {
                    MemberID = x.MemberID,
                    MemberName = x.Name,
                    PhoneNumber = x.PhoneNumber,
                    StartDate = x.StartDate,
                    EndDate = x.EndDate,
                    RemainingDays = (x.EndDate > now) ? (int)Math.Ceiling((x.EndDate - now).TotalDays) : 0,
                    Branch = x.Branch
                }).ToList();

                var groupedResult = projected
                    .GroupBy(r => new { r.MemberID, r.Branch })
                    .Select(g => new MemberRemainingDayDto
                    {
                        MemberID = g.Key.MemberID,
                        MemberName = g.First().MemberName,
                        PhoneNumber = g.First().PhoneNumber,
                        StartDate = g.Min(r => r.StartDate),
                        EndDate = g.Max(r => r.EndDate),
                        RemainingDays = g.Sum(r => r.RemainingDays),
                        Message = $"Hoşgeldiniz. {g.First().MemberName.ToUpper()}. {g.Key.Branch} branşında toplam {g.Sum(r => r.RemainingDays)} gün kaldı.",
                        Branch = g.Key.Branch
                    })
                    .Where(dto => dto.RemainingDays > 0 && dto.RemainingDays <= 7)
                    .OrderBy(x => x.MemberName)
                    .ToList();

                return groupedResult;
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<List<MemberEntryDto>> GetMemberEntriesBySearchAsync(string searchText, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var list = await (from m in _context.Members.AsNoTracking()
                                  join ms in _context.Memberships.AsNoTracking() on m.MemberID equals ms.MemberID
                                  join eh in _context.EntryExitHistories.AsNoTracking() on ms.MembershipID equals eh.MembershipID
                                  where m.IsActive == true && eh.EntryDate.HasValue &&
                                        (m.Name.Contains(searchText) || m.PhoneNumber.Contains(searchText)) &&
                                        m.CompanyID == companyId && ms.CompanyID == companyId && eh.CompanyID == companyId
                                  select new MemberEntryDto
                                  {
                                      MemberID = m.MemberID,
                                      Name = m.Name,
                                      PhoneNumber = m.PhoneNumber,
                                      EntryTime = eh.EntryDate.Value,
                                      ExitTime = eh.ExitDate
                                  })
                                 .OrderByDescending(x => x.EntryTime)
                                 .ToListAsync(ct);
                return list;
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<PaginatedResult<MemberEntryDto>> GetMemberEntriesBySearchPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var baseQuery = from m in _context.Members.AsNoTracking()
                                join ms in _context.Memberships.AsNoTracking() on m.MemberID equals ms.MemberID
                                join eh in _context.EntryExitHistories.AsNoTracking() on ms.MembershipID equals eh.MembershipID
                                where m.IsActive == true && eh.EntryDate.HasValue &&
                                      m.CompanyID == companyId && ms.CompanyID == companyId && eh.CompanyID == companyId
                                select new { m, ms, eh };

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    baseQuery = baseQuery.Where(x => x.m.Name.Contains(parameters.SearchText) || x.m.PhoneNumber.Contains(parameters.SearchText));
                }
                if (parameters.Date.HasValue)
                {
                    var date = parameters.Date.Value.Date;
                    baseQuery = baseQuery.Where(x => x.eh.EntryDate.Value.Date == date);
                }

                var ordered = baseQuery.Select(x => new MemberEntryDto
                {
                    MemberID = x.m.MemberID,
                    Name = x.m.Name,
                    PhoneNumber = x.m.PhoneNumber,
                    EntryTime = x.eh.EntryDate.Value,
                    ExitTime = x.eh.ExitDate
                }).OrderByDescending(x => x.EntryTime);

                var totalCount = await ordered.CountAsync(ct);
                var items = await ordered
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync(ct);

                return new PaginatedResult<MemberEntryDto>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }














        // Async (CT'li) — Dashboard/Metrics
        public async Task<IDataResult<int>> GetTotalActiveMembersAsync(int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    var totalActive = await _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId)
                        .Join(_context.Memberships,
                              m => m.MemberID,
                              ms => ms.MemberID,
                              (m, ms) => new { Member = m, Membership = ms })
                        .Where(joined => joined.Membership.IsActive == true &&
                                         joined.Membership.EndDate > DateTime.Now &&
                                         joined.Membership.IsFrozen == false &&
                                         joined.Membership.CompanyID == companyId)
                        .Select(joined => joined.Member.MemberID)
                        .Distinct()
                        .CountAsync(ct);

                    return new SuccessDataResult<int>(totalActive);
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<int>($"Aktif üye sayısı (async) getirilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IDataResult<int>> GetTotalRegisteredMembersAsync(int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    var totalRegistered = await _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId)
                        .AsNoTracking()
                        .CountAsync(ct);

                    return new SuccessDataResult<int>(totalRegistered);
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<int>($"Kayıtlı üye sayısı (async) getirilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IDataResult<Dictionary<string, int>>> GetActiveMemberCountsAsync(int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    var today = DateTime.Now;

                    var maleCount = await _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId && m.Gender == 1)
                        .Join(_context.Memberships,
                              m => m.MemberID,
                              ms => ms.MemberID,
                              (m, ms) => new { Member = m, Membership = ms })
                        .Where(joined => joined.Membership.IsActive == true &&
                                         joined.Membership.EndDate > today &&
                                         joined.Membership.IsFrozen == false &&
                                         joined.Membership.CompanyID == companyId)
                        .Select(joined => joined.Member.MemberID)
                        .Distinct()
                        .CountAsync(ct);

                    var femaleCount = await _context.Members
                        .Where(m => m.IsActive == true && m.CompanyID == companyId && m.Gender == 2)
                        .Join(_context.Memberships,
                              m => m.MemberID,
                              ms => ms.MemberID,
                              (m, ms) => new { Member = m, Membership = ms })
                        .Where(joined => joined.Membership.IsActive == true &&
                                         joined.Membership.EndDate > today &&
                                         joined.Membership.IsFrozen == false &&
                                         joined.Membership.CompanyID == companyId)
                        .Select(joined => joined.Member.MemberID)
                        .Distinct()
                        .CountAsync(ct);

                    var counts = new Dictionary<string, int>
                    {
                        { "male", maleCount },
                        { "female", femaleCount }
                    };

                    return new SuccessDataResult<Dictionary<string, int>>(counts);
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, int>>($"Aktif üye sayıları (async) getirilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IDataResult<Dictionary<string, int>>> GetBranchCountsAsync(int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    var list = await (from m in _context.Members
                                      join ms in _context.Memberships on m.MemberID equals ms.MemberID
                                      join mt in _context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                                      where ms.IsActive == true &&
                                            ms.EndDate > DateTime.Now &&
                                            ms.IsFrozen == false &&
                                            m.CompanyID == companyId &&
                                            ms.CompanyID == companyId &&
                                            mt.CompanyID == companyId &&
                                            m.IsActive == true
                                      select new { m.MemberID, mt.Branch })
                                      .AsNoTracking()
                                      .ToListAsync(ct);

                    var branchCounts = list
                        .GroupBy(x => x.Branch)
                        .ToDictionary(g => g.Key, g => g.Select(y => y.MemberID).Distinct().Count());

                    return new SuccessDataResult<Dictionary<string, int>>(branchCounts);
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<Dictionary<string, int>>($"Şube sayıları (async) getirilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<List<MemberBirthdayDto>> GetUpcomingBirthdaysAsync(int days, CancellationToken ct = default)
        {
            if (_context != null)
            {
                ct.ThrowIfCancellationRequested();

                int companyId = _companyContext.GetCompanyId();
                var today = DateTime.Now.Date;

                var members = await _context.Members
                    .Where(m => m.IsActive == true && m.CompanyID == companyId && m.BirthDate != null)
                    .AsNoTracking()
                    .ToListAsync(ct);

                var result = members
                    .Select(m => new
                    {
                        Member = m,
                        DaysUntilBirthday = CalculateDaysUntilBirthday(m.BirthDate, today)
                    })
                    .Where(x => x.DaysUntilBirthday <= days)
                    .OrderBy(x => x.DaysUntilBirthday)
                    .Select(x => new MemberBirthdayDto
                    {
                        MemberID = x.Member.MemberID,
                        Name = x.Member.Name,
                        PhoneNumber = x.Member.PhoneNumber,
                        BirthDate = x.Member.BirthDate,
                        DaysUntilBirthday = x.DaysUntilBirthday
                    })
                    .ToList();

                return result;
            }

            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }



        public async Task<IResult> UpdateMemberProfileAsync(int userId, int companyId, MemberProfileUpdateDto profileUpdateDto, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                if (_context != null)
                {
                    // User bilgilerini al ve güncelle
                    var user = await _context.Users.FirstOrDefaultAsync(u => u.UserID == userId && u.IsActive, ct);
                    if (user == null)
                    {
                        return new ErrorResult("Kullanıcı bulunamadı.");
                    }

                    // Kullanıcının aktif member kaydını al (multi-tenant: sadece mevcut şirketteki kayıt)
                    var member = await _context.Members.FirstOrDefaultAsync(m => m.UserID == userId && m.IsActive == true && m.CompanyID == companyId, ct);
                    if (member == null)
                    {
                        return new ErrorResult("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                    }

                    // User tablosunu güncelle (FirstName, LastName)
                    if (!string.IsNullOrWhiteSpace(profileUpdateDto.FirstName))
                    {
                        user.FirstName = profileUpdateDto.FirstName.Trim();
                    }
                    if (!string.IsNullOrWhiteSpace(profileUpdateDto.LastName))
                    {
                        user.LastName = profileUpdateDto.LastName.Trim();
                    }
                    user.UpdatedDate = DateTime.Now;

                    // Member tablosunu güncelle (Adress, BirthDate)
                    member.Adress = profileUpdateDto.Adress?.Trim();
                    member.BirthDate = profileUpdateDto.BirthDate;
                    member.UpdatedDate = DateTime.Now;

                    // Değişiklikleri kaydet
                    await _context.SaveChangesAsync(ct);

                    return new SuccessResult("Profil bilgileriniz başarıyla güncellendi.");
                }

                // DI kullanılmıyorsa exception fırlat (artık backward compatibility yok)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (OperationCanceledException)
            {
                return new ErrorResult("İşlem iptal edildi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Profil güncellenirken hata oluştu: {ex.Message}");
            }
        }



        public async Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByPhoneNumberAsync(string phoneNumber, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                if (_context != null)
                {
                    var member = await _context.Members.AsNoTracking().FirstOrDefaultAsync(m => m.PhoneNumber == phoneNumber && m.IsActive == true, ct);
                    if (member == null)
                    {
                        return new ErrorDataResult<GetMemberQRByPhoneNumberDto>("Telefon Numarasını Kontrol Ediniz.");
                    }

                    int memberCompanyId = member.CompanyID;
                    var now = DateTime.Now;

                    var allMemberships = await _context.Memberships
                        .AsNoTracking()
                        .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == memberCompanyId)
                        .OrderBy(m => m.StartDate)
                        .ToListAsync(ct);

                    var membershipTypes = await _context.MembershipTypes
                        .AsNoTracking()
                        .Where(mt => mt.CompanyID == memberCompanyId)
                        .ToDictionaryAsync(mt => mt.MembershipTypeID, ct);

                    var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                    if (frozenMembership != null)
                    {
                        return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                            new GetMemberQRByPhoneNumberDto
                            {
                                Name = member.Name,
                                ScanNumber = member.ScanNumber,
                                IsFrozen = true,
                                FreezeEndDate = frozenMembership.FreezeEndDate,
                                RemainingDays = "Dondurulmuş",
                                Memberships = new List<MembershipInfo>(),
                                PhoneNumber = member.PhoneNumber
                            },
                            $"Üyelik dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                        );
                    }

                    var consolidatedMemberships = new Dictionary<string, MembershipInfo>();
                    foreach (var membership in allMemberships)
                    {
                        if (membershipTypes.TryGetValue(membership.MembershipTypeID, out var membershipType))
                        {
                            var branch = membershipType.Branch;
                            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);

                            if (consolidatedMemberships.ContainsKey(branch))
                            {
                                if (membership.StartDate < consolidatedMemberships[branch].StartDate)
                                    consolidatedMemberships[branch].StartDate = membership.StartDate;
                                if (membership.EndDate > consolidatedMemberships[branch].EndDate)
                                    consolidatedMemberships[branch].EndDate = membership.EndDate;
                                consolidatedMemberships[branch].RemainingDays += Math.Max(0, remainingDays);
                            }
                            else
                            {
                                consolidatedMemberships[branch] = new MembershipInfo
                                {
                                    Branch = branch,
                                    StartDate = membership.StartDate,
                                    EndDate = membership.EndDate,
                                    RemainingDays = Math.Max(0, remainingDays)
                                };
                            }
                        }
                    }

                    var message = consolidatedMemberships.Count > 0 && consolidatedMemberships.Values.Any(m => m.RemainingDays > 0)
                        ? $"Hoşgeldiniz {member.Name.ToUpper()}. Toplam {consolidatedMemberships.Values.Sum(m => m.RemainingDays)} gün kaldı."
                        : $"Hoşgeldiniz {member.Name.ToUpper()}. Üyeliğinizin süresi dolmuş.";

                    return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                        new GetMemberQRByPhoneNumberDto
                        {
                            Name = member.Name,
                            ScanNumber = member.ScanNumber,
                            RemainingDays = consolidatedMemberships.Count > 0
                                ? consolidatedMemberships.Values.Sum(m => m.RemainingDays).ToString()
                                : "Süresi Dolmuş",
                            Memberships = consolidatedMemberships.Values.ToList(),
                            IsFrozen = false,
                            FreezeEndDate = null,
                            PhoneNumber = member.PhoneNumber,
                            Message = message
                        },
                        message
                    );
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<GetMemberQRByPhoneNumberDto>($"QR kod bilgileri getirilirken hata oluştu: {ex.Message}");
            }
        }




        public async Task<IDataResult<MemberDetailDto>> GetMemberRemainingDaysForScanNumberAsync(string scanNumber, int companyId, CancellationToken ct = default)
        {
            try
            {
                if (_context != null)
                {
                    ct.ThrowIfCancellationRequested();
                    var member = await _context.Members.FirstOrDefaultAsync(m => m.ScanNumber == scanNumber && m.IsActive == true, ct);
                    if (member == null)
                    {
                        return new ErrorDataResult<MemberDetailDto>("Geçersiz QR kod.");
                    }

                    int memberCompanyId = member.CompanyID;
                    var now = DateTime.Now;

                    var allMemberships = await _context.Memberships
                        .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == memberCompanyId)
                        .OrderBy(m => m.StartDate)
                        .ToListAsync(ct);

                    var membershipTypes = await _context.MembershipTypes
                        .Where(mt => mt.CompanyID == memberCompanyId)
                        .ToDictionaryAsync(mt => mt.MembershipTypeID, ct);

                    var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                    if (frozenMembership != null)
                    {
                        var frozenDto = new MemberDetailDto
                        {
                            MemberID = member.MemberID,
                            MemberName = member.Name,
                            PhoneNumber = member.PhoneNumber,
                            Message = $"Üyelik dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}",
                            Memberships = new List<MembershipDetailDto>()
                        };
                        return new SuccessDataResult<MemberDetailDto>(frozenDto, frozenDto.Message);
                    }

                    var membershipDetails = new List<MembershipDetailDto>();
                    foreach (var membership in allMemberships.Where(m => m.EndDate > now))
                    {
                        if (membershipTypes.TryGetValue(membership.MembershipTypeID, out var membershipType))
                        {
                            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
                            membershipDetails.Add(new MembershipDetailDto
                            {
                                Branch = membershipType.Branch,
                                StartDate = membership.StartDate,
                                EndDate = membership.EndDate,
                                RemainingDays = remainingDays
                            });
                        }
                    }

                    var activeMembership = allMemberships
                        .Where(m => m.StartDate <= now && m.EndDate > now)
                        .OrderBy(m => m.StartDate)
                        .FirstOrDefault();

                    var futureMembership = allMemberships
                        .Where(m => m.StartDate > now)
                        .OrderBy(m => m.StartDate)
                        .FirstOrDefault();

                    var memberDetail = new MemberDetailDto
                    {
                        MemberID = member.MemberID,
                        MemberName = member.Name,
                        PhoneNumber = member.PhoneNumber,
                        Memberships = membershipDetails
                    };

                    if (activeMembership == null && futureMembership != null)
                    {
                        memberDetail.Message = $"Üyeliğinizin başlama tarihi: {futureMembership.StartDate:dd.MM.yyyy}";
                        return new SuccessDataResult<MemberDetailDto>(memberDetail, memberDetail.Message);
                    }

                    if (activeMembership == null)
                    {
                        memberDetail.Message = "Üyeliğinizin süresi dolmuştur.";
                        return new SuccessDataResult<MemberDetailDto>(memberDetail, memberDetail.Message);
                    }

                    var lastEntryHistory = await _context.EntryExitHistories
                        .Where(e => e.MembershipID == activeMembership.MembershipID)
                        .OrderByDescending(e => e.EntryDate)
                        .FirstOrDefaultAsync(ct);

                    if (lastEntryHistory == null || lastEntryHistory.ExitDate.HasValue)
                    {
                        var entryHistory = new EntryExitHistory
                        {
                            MembershipID = activeMembership.MembershipID,
                            CompanyID = memberCompanyId,
                            EntryDate = now,
                            IsActive = true,
                            CreationDate = now
                        };
                        await _context.EntryExitHistories.AddAsync(entryHistory, ct);
                        await _context.SaveChangesAsync(ct);
                        memberDetail.Message = "Hoşgeldiniz " + member.Name.ToUpper();
                    }
                    else if (lastEntryHistory.EntryDate.HasValue)
                    {
                        var timeSinceLastEntry = now - lastEntryHistory.EntryDate.Value;
                        if (timeSinceLastEntry.TotalMinutes > 301)
                        {
                            lastEntryHistory.ExitDate = lastEntryHistory.EntryDate.Value.AddMinutes(301);
                            lastEntryHistory.IsActive = false;
                            lastEntryHistory.UpdatedDate = now;
                            lastEntryHistory.CompanyID = memberCompanyId;
                            _context.EntryExitHistories.Update(lastEntryHistory);
                            await _context.SaveChangesAsync(ct);

                            var newEntryHistory = new EntryExitHistory
                            {
                                MembershipID = activeMembership.MembershipID,
                                CompanyID = memberCompanyId,
                                EntryDate = now,
                                IsActive = true,
                                CreationDate = now
                            };
                            await _context.EntryExitHistories.AddAsync(newEntryHistory, ct);
                            await _context.SaveChangesAsync(ct);
                            memberDetail.Message = "Hoşgeldiniz " + member.Name.ToUpper();
                        }
                        else
                        {
                            lastEntryHistory.ExitDate = now;
                            lastEntryHistory.IsActive = false;
                            lastEntryHistory.UpdatedDate = now;
                            lastEntryHistory.CompanyID = memberCompanyId;
                            _context.EntryExitHistories.Update(lastEntryHistory);
                            await _context.SaveChangesAsync(ct);
                            memberDetail.Message = "Güle güle " + member.Name.ToUpper();
                        }
                    }

                    return new SuccessDataResult<MemberDetailDto>(memberDetail, memberDetail.Message);
                }

                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberDetailDto>($"İşlem sırasında hata oluştu: {ex.Message}");
            }
        }


        // SOLID prensiplerine uygun refactoring için eklenen metotlar

        // Sync AddMemberWithUserManagement kaldırıldı (async sürüm mevcut)

        // Async varyant
        public async Task<IResult> AddMemberWithUserManagementAsync(Member member, int companyId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context == null) throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            // CompanyID ata
            member.CompanyID = companyId;
            member.CreationDate = DateTime.Now;
            member.IsActive = true;

            // QR kod üret
            member.ScanNumber = await GenerateUniqueQRCodeAsync(_context, ct);

            // E-posta adresi kontrolü
            if (!string.IsNullOrEmpty(member.Email))
            {
                var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Email == member.Email, ct);
                if (existingUser != null)
                {
                    member.UserID = existingUser.UserID;
                }
                else
                {
                    var newUser = await CreateNewUserForMemberAsync(member, _context, ct);
                    if (newUser != null)
                    {
                        member.UserID = newUser.UserID;
                    }
                }
            }

            await _context.Members.AddAsync(member, ct);
            await _context.SaveChangesAsync(ct);
            return new SuccessResult("Üye başarıyla eklendi.");
        }

        // Sync AddMemberWithCard kaldırıldı (async sürüm mevcut)

        // Async varyant
        public async Task<IResult> AddMemberWithCardAsync(Member member, int companyId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context == null) throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            // CompanyID ata
            member.CompanyID = companyId;
            member.CreationDate = DateTime.Now;
            member.IsActive = true;

            if (string.IsNullOrEmpty(member.ScanNumber))
            {
                return new ErrorResult("Kart numarası boş olamaz.");
            }

            var existingMemberWithCard = await _context.Members
                .FirstOrDefaultAsync(m => m.ScanNumber == member.ScanNumber && m.CompanyID == companyId, ct);
            if (existingMemberWithCard != null)
            {
                return new ErrorResult("Bu kart numarası zaten başka bir üye tarafından kullanılıyor.");
            }

            if (!string.IsNullOrEmpty(member.Email))
            {
                var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.Email == member.Email, ct);
                if (existingUser != null)
                {
                    member.UserID = existingUser.UserID;
                }
                else
                {
                    var newUser = await CreateNewUserForMemberAsync(member, _context, ct);
                    if (newUser != null)
                    {
                        member.UserID = newUser.UserID;
                    }
                }
            }

            var existingMemberWithPhone = await _context.Members
                .FirstOrDefaultAsync(m => m.PhoneNumber == member.PhoneNumber && m.CompanyID == companyId, ct);
            if (existingMemberWithPhone != null)
            {
                return new ErrorResult("Bu telefon numarası zaten kayıtlı.");
            }

            await _context.Members.AddAsync(member, ct);
            await _context.SaveChangesAsync(ct);
            return new SuccessResult("Kartlı üye başarıyla eklendi.");
        }



        // Sync DeleteMemberWithUserManagement kaldırıldı (async sürüm mevcut)

        // Async varyant
        public async Task<IResult> DeleteMemberWithUserManagementAsync(int memberId, int companyId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context == null) throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            var member = await _context.Members.FirstOrDefaultAsync(m => m.MemberID == memberId && m.CompanyID == companyId, ct);
            if (member == null)
            {
                return new ErrorResult("Üye bulunamadı veya erişim yetkiniz yok.");
            }

            member.IsActive = false;
            member.DeletedDate = DateTime.Now;

            if (member.UserID.HasValue)
            {
                var otherActiveMembers = await _context.Members
                    .Where(m => m.UserID == member.UserID && m.MemberID != memberId && m.IsActive == true)
                    .ToListAsync(ct);

                if (otherActiveMembers.Count == 0)
                {
                    var user = await _context.Users.FirstOrDefaultAsync(u => u.UserID == member.UserID.Value, ct);
                    if (user != null)
                    {
                        user.RequirePasswordChange = true;
                        user.UpdatedDate = DateTime.Now;
                    }
                }
            }

            await _context.SaveChangesAsync(ct);
            return new SuccessResult("Üye başarıyla silindi.");
        }



        public async Task<IDataResult<MemberDetailWithHistoryDto>> GetMemberDetailByIdWithCalculationsAsync(int memberId, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                if (_context != null)
                {
                    // Önce member'ın bu company'ye ait olup olmadığını kontrol et (AsNoTracking)
                    var member = await _context.Members.AsNoTracking().FirstOrDefaultAsync(m => m.MemberID == memberId && m.CompanyID == companyId, ct);
                    if (member == null)
                    {
                        return new ErrorDataResult<MemberDetailWithHistoryDto>("Üye bulunamadı veya erişim yetkiniz yok.");
                    }

                    // Mevcut sync hesaplamayı referans alarak async olarak yeniden üret
                    // Üye temel bilgileri
                    var memberships = await (from ms in _context.Memberships.AsNoTracking()
                                             join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                             where ms.MemberID == memberId && ms.CompanyID == companyId && mt.CompanyID == companyId && ms.DeletedDate == null && mt.DeletedDate == null
                                             select new MembershipHistoryDto
                                             {
                                                 MembershipID = ms.MembershipID,
                                                 MembershipTypeID = ms.MembershipTypeID,
                                                 TypeName = mt.TypeName,
                                                 Branch = mt.Branch,
                                                 Day = mt.Day,
                                                 Price = mt.Price,
                                                 StartDate = ms.StartDate,
                                                 EndDate = ms.EndDate,
                                                 IsActive = ms.IsActive == true,
                                                 IsFrozen = ms.IsFrozen,
                                                 FreezeStartDate = ms.FreezeStartDate,
                                                 FreezeEndDate = ms.FreezeEndDate,
                                                 FreezeDays = ms.FreezeDays,
                                                 OriginalEndDate = ms.OriginalEndDate,
                                                 CreationDate = ms.CreationDate
                                             }).ToListAsync(ct);

                    var payments = await (from p in _context.Payments.AsNoTracking()
                                          join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                          join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                          where ms.MemberID == memberId && p.CompanyID == companyId && ms.CompanyID == companyId && mt.CompanyID == companyId && ms.DeletedDate == null && p.DeletedDate == null && mt.DeletedDate == null
                                          select new PaymentHistoryItemDto
                                          {
                                              PaymentID = p.PaymentID,
                                              MembershipID = ms.MembershipID,
                                              MembershipTypeName = mt.TypeName,
                                              Branch = mt.Branch,
                                              PaymentDate = p.PaymentDate,
                                              PaymentAmount = p.PaymentAmount,
                                              PaymentMethod = p.PaymentMethod,
                                              PaymentStatus = p.PaymentStatus
                                          }).ToListAsync(ct);

                    var entryExitHistory = await (from eh in _context.EntryExitHistories.AsNoTracking()
                                                  join ms in _context.Memberships.AsNoTracking() on eh.MembershipID equals ms.MembershipID
                                                  join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                                  where ms.MemberID == memberId && eh.CompanyID == companyId && ms.CompanyID == companyId && mt.CompanyID == companyId && ms.DeletedDate == null && mt.DeletedDate == null
                                                  select new EntryExitHistoryItemDto
                                                  {
                                                      EntryExitID = eh.EntryExitID,
                                                      MembershipID = ms.MembershipID,
                                                      MembershipTypeName = mt.TypeName,
                                                      Branch = mt.Branch,
                                                      EntryDate = eh.EntryDate,
                                                      ExitDate = eh.ExitDate
                                                  }).ToListAsync(ct);

                    var freezeHistory = await (from fh in _context.MembershipFreezeHistory.AsNoTracking()
                                               join ms in _context.Memberships.AsNoTracking() on fh.MembershipID equals ms.MembershipID
                                               join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                               where ms.MemberID == memberId && fh.CompanyID == companyId && ms.CompanyID == companyId && mt.CompanyID == companyId && ms.DeletedDate == null && mt.DeletedDate == null
                                               select new MembershipFreezeHistoryItemDto
                                               {
                                                   FreezeHistoryID = fh.FreezeHistoryID,
                                                   MembershipID = ms.MembershipID,
                                                   MembershipTypeName = mt.TypeName,
                                                   Branch = mt.Branch,
                                                   StartDate = fh.StartDate,
                                                   PlannedEndDate = fh.PlannedEndDate,
                                                   ActualEndDate = fh.ActualEndDate,
                                                   FreezeDays = fh.FreezeDays,
                                                   UsedDays = fh.UsedDays,
                                                   CancellationType = fh.CancellationType
                                               }).ToListAsync(ct);

                    var lastMembershipEndDate = await _context.Memberships.AsNoTracking()
                        .Where(ms => ms.MemberID == memberId && ms.CompanyID == companyId)
                        .OrderByDescending(ms => ms.EndDate)
                        .Select(ms => (DateTime?)ms.EndDate)
                        .FirstOrDefaultAsync(ct);

                    var dto = new MemberDetailWithHistoryDto
                    {
                        MemberID = member.MemberID,
                        UserID = member.UserID,
                        Name = member.Name,
                        Gender = member.Gender,
                        PhoneNumber = member.PhoneNumber,
                        Adress = member.Adress,
                        BirthDate = member.BirthDate,
                        Email = member.Email,
                        IsActive = member.IsActive,
                        ScanNumber = member.ScanNumber,
                        Balance = member.Balance,
                        CreationDate = member.CreationDate,
                        Memberships = memberships,
                        Payments = payments,
                        EntryExitHistory = entryExitHistory,
                        FreezeHistory = freezeHistory,
                        LastMembershipEndDate = lastMembershipEndDate
                    };

                    var activeMembership = dto.Memberships?
                        .Where(m => m.IsActive && !m.IsFrozen && m.EndDate > DateTime.Now)
                        .OrderByDescending(m => m.EndDate)
                        .FirstOrDefault();

                    dto.RemainingDays = activeMembership != null
                        ? (int?)Math.Ceiling((activeMembership.EndDate - DateTime.Now).TotalDays)
                        : null;

                    return new SuccessDataResult<MemberDetailWithHistoryDto>(dto);
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberDetailWithHistoryDto>($"Üye detayı getirilirken hata oluştu: {ex.Message}");
            }
        }


        // Sync UpdateMemberWithUserManagement kaldırıldı (async sürüm mevcut)

        // Async varyant
        public async Task<IResult> UpdateMemberWithUserManagementAsync(Member member, int companyId, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context == null) throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            var existingMember = await _context.Members.FirstOrDefaultAsync(m => m.MemberID == member.MemberID && m.CompanyID == companyId, ct);
            if (existingMember == null)
            {
                return new ErrorResult("Üye bulunamadı veya erişim yetkiniz yok.");
            }

            member.CompanyID = companyId;

            // ✅ EMAIL SİLME DURUMU (AYHAN KOSAR SENARYOsu)
            if (string.IsNullOrEmpty(member.Email) && !string.IsNullOrEmpty(existingMember.Email))
            {
                // ✅ EMAIL SİLİNDİ - USER BAĞLANTISINI KOP
                if (existingMember.UserID.HasValue)
                {
                    // ✅ USER'DA REQUIREPASSWORDCHANGE = TRUE YAP
                    var existingUser = await _context.Users.FirstOrDefaultAsync(u => u.UserID == existingMember.UserID.Value, ct);
                    if (existingUser != null)
                    {
                        existingUser.RequirePasswordChange = true;
                        existingUser.UpdatedDate = DateTime.Now;
                        
                        // ✅ LOG: Email bağlantısı kesildi
                        Console.WriteLine($"[{DateTime.Now}] Email connection removed for User {existingUser.UserID}, RequirePasswordChange set to true");
                    }
                }
                
                // ✅ MEMBER'DAN USER BAĞLANTISINI KOP
                member.UserID = null;
            }
            // ✅ EMAIL DEĞİŞTİRME DURUMU
            else if (!string.IsNullOrEmpty(member.Email) && existingMember.Email != member.Email)
            {
                // ✅ ESKİ USER BAĞLANTISINI KOP
                if (existingMember.UserID.HasValue)
                {
                    // Başka aktif member'ları kontrol et
                    var otherActiveMembers = await _context.Members
                        .Where(m => m.UserID == existingMember.UserID && m.MemberID != member.MemberID && m.IsActive == true)
                        .ToListAsync(ct);

                    if (otherActiveMembers.Count == 0)
                    {
                        // ✅ ESKİ USER'DA REQUIREPASSWORDCHANGE = TRUE YAP
                        var oldUser = await _context.Users.FirstOrDefaultAsync(u => u.UserID == existingMember.UserID.Value, ct);
                        if (oldUser != null)
                        {
                            oldUser.RequirePasswordChange = true;
                            oldUser.UpdatedDate = DateTime.Now;
                        }
                    }
                }

                // ✅ YENİ EMAIL İÇİN USER KONTROL ET
                var existingUserWithNewEmail = await _context.Users.FirstOrDefaultAsync(u => u.Email == member.Email, ct);
                if (existingUserWithNewEmail != null)
                {
                    member.UserID = existingUserWithNewEmail.UserID;
                }
                else
                {
                    var newUser = await CreateNewUserForMemberAsync(member, _context, ct);
                    if (newUser != null)
                    {
                        member.UserID = newUser.UserID;
                    }
                }
            }
            // ✅ EMAIL DEĞİŞMEDİ
            else if (!string.IsNullOrEmpty(member.Email))
            {
                member.UserID = existingMember.UserID;
            }
            // ✅ EMAIL ZATEN YOKTU VE HALA YOK
            else
            {
                member.UserID = null;
            }

            // ✅ MEMBER BİLGİLERİNİ GÜNCELLE
            existingMember.Name = member.Name;
            existingMember.Email = member.Email;
            existingMember.PhoneNumber = member.PhoneNumber;
            existingMember.Adress = member.Adress;
            existingMember.BirthDate = member.BirthDate;
            existingMember.Gender = member.Gender;
            existingMember.UserID = member.UserID;
            existingMember.Balance = member.Balance;
            existingMember.UpdatedDate = DateTime.Now;

            await _context.SaveChangesAsync(ct);
            return new SuccessResult("Üye başarıyla güncellendi.");
        }


        // Async helper metotlar
        private async Task<string> GenerateUniqueQRCodeAsync(GymContext context, CancellationToken ct)
        {
            string qrCode;
            do
            {
                ct.ThrowIfCancellationRequested();
                var prefix = "MBR";
                var timestamp = DateTime.Now.ToString("yyMMddHH");
                var randomPart = GenerateRandomPart(12);
                qrCode = $"{prefix}{timestamp}{randomPart}";
            } while (await context.Members.AnyAsync(m => m.ScanNumber == qrCode, ct));

            return qrCode;
        }

        private async Task<User> CreateNewUserForMemberAsync(Member member, GymContext context, CancellationToken ct)
        {
            if (string.IsNullOrEmpty(member.PhoneNumber) || member.PhoneNumber.Length < 4)
                return null;

            string tempPassword = member.PhoneNumber.Substring(member.PhoneNumber.Length - 4);

            string firstName = member.Name;
            string lastName = "";
            int spaceIndex = member.Name.IndexOf(' ');
            if (spaceIndex > 0)
            {
                firstName = member.Name.Substring(0, spaceIndex);
                lastName = member.Name.Substring(spaceIndex + 1);
            }

            byte[] passwordHash, passwordSalt;
            HashingHelper.CreatePasswordHash(tempPassword, out passwordHash, out passwordSalt);

            var user = new User
            {
                Email = member.Email,
                FirstName = firstName,
                LastName = lastName,
                PasswordHash = passwordHash,
                PasswordSalt = passwordSalt,
                IsActive = true,
                RequirePasswordChange = true,
                CreationDate = DateTime.Now
            };

            await context.Users.AddAsync(user, ct);
            await context.SaveChangesAsync(ct);

            var memberRole = await context.OperationClaims.FirstOrDefaultAsync(oc => oc.Name == "member", ct);
            if (memberRole != null)
            {
                var userOperationClaim = new UserOperationClaim
                {
                    UserId = user.UserID,
                    OperationClaimId = memberRole.OperationClaimId,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };
                await context.UserOperationClaims.AddAsync(userOperationClaim, ct);
                await context.SaveChangesAsync(ct);
            }

            return user;
        }

        // Async (CT'li) varyantlar — Sync implementasyonlar korunur
        public async Task<PaginatedResult<Member>> GetAllPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var query = _context.Members
                    .AsNoTracking()
                    .Where(x => x.IsActive == true && x.CompanyID == companyId);

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x => x.Name.Contains(parameters.SearchText) || x.PhoneNumber.Contains(parameters.SearchText));
                }
                if (parameters.Gender.HasValue)
                {
                    byte genderByte = (byte)parameters.Gender.Value;
                    query = query.Where(x => x.Gender == genderByte);
                }
                query = query.OrderByDescending(x => x.CreationDate);

                var totalCount = await query.CountAsync(ct);
                var items = await query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync(ct);
                return new PaginatedResult<Member>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<PaginatedResult<MemberFilter>> GetMemberDetailsPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var baseQuery = from m in _context.Members.AsNoTracking()
                                join ms in _context.Memberships.AsNoTracking() on m.MemberID equals ms.MemberID
                                join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                where m.IsActive == true
                                      && ms.IsActive == true && ms.EndDate > DateTime.Now && ms.IsFrozen == false
                                      && m.CompanyID == companyId
                                      && ms.CompanyID == companyId
                                      && mt.CompanyID == companyId
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    Gender = m.Gender,
                                    Branch = mt.Branch,
                                    RemainingDays = ms.StartDate > DateTime.Now
                                        ? EF.Functions.DateDiffDay(ms.StartDate, ms.EndDate)
                                        : EF.Functions.DateDiffDay(DateTime.Now, ms.EndDate),
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsActive = ms.IsActive == true,
                                    EffectiveUpdated = (ms.UpdatedDate ?? ms.CreationDate),
                                    IsFutureStartDate = ms.StartDate > DateTime.Now
                                };

                var groupedQuery = baseQuery
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.Gender, x.Branch })
                    .Select(g => new MemberFilter
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(x => x.MembershipID),
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        Gender = g.Key.Gender,
                        Branch = g.Key.Branch,
                        RemainingDays = g.Sum(x => x.RemainingDays),
                        StartDate = g.Min(x => x.StartDate),
                        EndDate = g.Max(x => x.EndDate),
                        IsActive = true,
                        UpdatedDate = g.Max(x => x.EffectiveUpdated),
                        IsFutureStartDate = g.Any(x => x.IsFutureStartDate)
                    });

                var filteredQuery = groupedQuery;
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    filteredQuery = filteredQuery.Where(x => x.Name.ToLower().Contains(searchText) || x.PhoneNumber.Contains(searchText));
                }
                if (parameters.Gender.HasValue)
                {
                    byte genderByte = (byte)parameters.Gender.Value;
                    filteredQuery = filteredQuery.Where(x => x.Gender == genderByte);
                }
                if (!string.IsNullOrWhiteSpace(parameters.Branch))
                {
                    filteredQuery = filteredQuery.Where(x => x.Branch == parameters.Branch);
                }

                var orderedQuery = filteredQuery
                    .OrderByDescending(x => x.UpdatedDate)
                    .ThenByDescending(x => x.MembershipID);

                var totalCount = await orderedQuery.CountAsync(ct);
                var items = await orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync(ct);

                return new PaginatedResult<MemberFilter>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<PaginatedResult<MemberFilter>> GetMembersByMultiplePackagesAsync(MemberPagingParameters parameters, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var baseQuery = from m in _context.Members.AsNoTracking()
                                join ms in _context.Memberships.AsNoTracking() on m.MemberID equals ms.MemberID
                                join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                where m.IsActive == true
                                      && ms.IsActive == true && ms.EndDate > DateTime.Now && ms.IsFrozen == false
                                      && m.CompanyID == companyId
                                      && ms.CompanyID == companyId
                                      && mt.CompanyID == companyId
                                      && parameters.MembershipTypeIds.Contains(ms.MembershipTypeID)
                                select new
                                {
                                    MemberID = m.MemberID,
                                    MembershipID = ms.MembershipID,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    Gender = m.Gender,
                                    Branch = mt.Branch,
                                    RemainingDays = ms.StartDate > DateTime.Now
                                        ? EF.Functions.DateDiffDay(ms.StartDate, ms.EndDate)
                                        : EF.Functions.DateDiffDay(DateTime.Now, ms.EndDate),
                                    StartDate = ms.StartDate,
                                    EndDate = ms.EndDate,
                                    IsActive = ms.IsActive == true,
                                    UpdatedDate = ms.UpdatedDate,
                                    CreationDate = ms.CreationDate,
                                    IsFutureStartDate = ms.StartDate > DateTime.Now
                                };
                var groupedQuery = baseQuery.AsEnumerable()
                    .GroupBy(x => new { x.MemberID, x.Name, x.PhoneNumber, x.Gender, x.Branch })
                    .Select(g => new MemberFilter
                    {
                        MemberID = g.Key.MemberID,
                        MembershipID = g.Max(x => x.MembershipID),
                        Name = g.Key.Name,
                        PhoneNumber = g.Key.PhoneNumber,
                        Gender = g.Key.Gender,
                        Branch = g.Key.Branch,
                        RemainingDays = g.Sum(x => x.RemainingDays),
                        StartDate = g.Min(x => x.StartDate),
                        EndDate = g.Max(x => x.EndDate),
                        IsActive = true,
                        UpdatedDate = g.Max(x => x.UpdatedDate ?? x.CreationDate),
                        IsFutureStartDate = g.Any(x => x.IsFutureStartDate)
                    });
                var filteredQuery = groupedQuery.AsQueryable();
                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    filteredQuery = filteredQuery.Where(x => x.Name.ToLower().Contains(searchText) || x.PhoneNumber.Contains(searchText));
                }
                if (parameters.Gender.HasValue)
                {
                    byte genderByte = (byte)parameters.Gender.Value;
                    filteredQuery = filteredQuery.Where(x => x.Gender == genderByte);
                }
                var orderedQuery = filteredQuery
                    .OrderByDescending(x => x.UpdatedDate)
                    .ThenByDescending(x => x.MembershipID);

                var totalCount = orderedQuery.Count();
                var items = orderedQuery
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToList();

                return await Task.FromResult(new PaginatedResult<MemberFilter>(items, parameters.PageNumber, parameters.PageSize, totalCount));
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<PaginatedResult<Member>> GetMembersWithBalancePaginatedAsync(MemberPagingParameters parameters, string balanceFilter, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var query = _context.Members
                    .AsNoTracking()
                    .Where(x => x.IsActive == true && x.CompanyID == companyId);

                // Bakiye filtreleme
                switch ((balanceFilter ?? "all").ToLower())
                {
                    case "positive":
                        query = query.Where(x => x.Balance > 0);
                        break;
                    case "zero":
                        query = query.Where(x => x.Balance == 0);
                        break;
                    case "negative":
                        query = query.Where(x => x.Balance < 0);
                        break;
                    default: // "all" ve diğer durumlar için: 0 bakiyeliler hariç
                        query = query.Where(x => x.Balance != 0);
                        break;
                }

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    query = query.Where(x => x.Name.Contains(parameters.SearchText) || x.PhoneNumber.Contains(parameters.SearchText));
                }

                // Sıralama - UpdatedDate desc, sonra Balance desc, sonra Name asc (deterministik)
                query = query.OrderByDescending(x => x.UpdatedDate ?? x.CreationDate)
                             .ThenByDescending(x => x.Balance)
                             .ThenBy(x => x.Name);

                var totalCount = await query.CountAsync(ct);
                var items = await query
                    .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                    .Take(parameters.PageSize)
                    .ToListAsync(ct);

                return new PaginatedResult<Member>(items, parameters.PageNumber, parameters.PageSize, totalCount);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }

        public async Task<List<GetActiveMemberDto>> GetActiveMembersAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                var result = from m in _context.Members.AsNoTracking()
                             where m.IsActive == true && m.CompanyID == companyId
                             select new GetActiveMemberDto
                             {
                                 MemberID = m.MemberID,
                                 Name = m.Name,
                                 Gender = m.Gender,
                                 PhoneNumber = m.PhoneNumber,
                                 IsActive = m.IsActive,
                                 Adress = m.Adress,
                                 BirthDate = m.BirthDate,
                                 Email = m.Email,
                                 ScanNumber = m.ScanNumber,
                                 Balance = m.Balance,
                             };
                return await result.ToListAsync(ct);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }


        // Legacy GetAll — Async (CT'li), CompanyID izolasyonlu, AsNoTracking ve deterministik sıralama
        public async Task<List<Member>> GetAllAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            if (_context != null)
            {
                int companyId = _companyContext.GetCompanyId();
                if (companyId <= 0)
                {
                    return new List<Member>();
                }

                return await _context.Members
                    .AsNoTracking()
                    .Where(m => m.CompanyID == companyId)
                    .OrderBy(m => m.MemberID)
                    .ToListAsync(ct);
            }
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }


        // Yeni Async muadiller (CT'li)
        public async Task<IDataResult<MemberProfileDto>> GetMemberProfileByUserIdAsync(int userId, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                if (_context != null)
                {
                    // Kullanıcının aktif member kaydını bul (gerçek companyId'yi member kaydından alıyoruz)
                    var userMember = await _context.Members
                        .AsNoTracking()
                        .FirstOrDefaultAsync(m => m.UserID == userId && m.IsActive == true, ct);

                    if (userMember == null)
                    {
                        return new ErrorDataResult<MemberProfileDto>("Üyelik bilgileriniz bulunamadı.");
                    }

                    int userCompanyId = userMember.CompanyID;

                    var profileDto = await (from u in _context.Users.AsNoTracking()
                                             join m in _context.Members.AsNoTracking() on u.UserID equals m.UserID
                                             where u.UserID == userId && u.IsActive == true && m.IsActive == true && m.CompanyID == userCompanyId
                                             select new MemberProfileDto
                                             {
                                                 FirstName = u.FirstName,
                                                 LastName = u.LastName,
                                                 Email = u.Email,
                                                 Adress = m.Adress,
                                                 BirthDate = m.BirthDate,
                                                 PhoneNumber = m.PhoneNumber,
                                                 ProfileImagePath = u.ProfileImagePath
                                             }).FirstOrDefaultAsync(ct);

                    if (profileDto == null)
                    {
                        return new ErrorDataResult<MemberProfileDto>("Kullanıcı bulunamadı veya üyelik bilgileriniz bulunamadı.");
                    }

                    return new SuccessDataResult<MemberProfileDto>(profileDto, "Profil bilgileri başarıyla getirildi.");
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MemberProfileDto>($"Profil bilgileri getirilirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByUserIdWithoutCompanyFilterAsync(int userId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                if (_context != null)
                {
                    var member = await _context.Members.AsNoTracking().FirstOrDefaultAsync(m => m.UserID == userId && m.IsActive == true, ct);
                    if (member == null)
                    {
                        return new ErrorDataResult<GetMemberQRByPhoneNumberDto>("Üyelik bilgileriniz bulunamadı. Lütfen spor salonunuzla iletişime geçin.");
                    }

                    int userCompanyId = member.CompanyID;
                    var now = DateTime.Now;

                    var allMemberships = await _context.Memberships
                        .AsNoTracking()
                        .Where(m => m.MemberID == member.MemberID && m.IsActive == true && m.CompanyID == userCompanyId)
                        .OrderBy(m => m.StartDate)
                        .ToListAsync(ct);

                    var membershipTypes = await _context.MembershipTypes
                        .AsNoTracking()
                        .Where(mt => mt.CompanyID == userCompanyId)
                        .ToDictionaryAsync(mt => mt.MembershipTypeID, ct);

                    var frozenMembership = allMemberships.FirstOrDefault(m => m.IsFrozen == true);
                    if (frozenMembership != null)
                    {
                        return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                            new GetMemberQRByPhoneNumberDto
                            {
                                Name = member.Name,
                                ScanNumber = member.ScanNumber,
                                IsFrozen = true,
                                FreezeEndDate = frozenMembership.FreezeEndDate,
                                RemainingDays = "Dondurulmuş",
                                Memberships = new List<MembershipInfo>(),
                                PhoneNumber = member.PhoneNumber
                            },
                            $"Üyeliğiniz dondurulmuştur. Açılış tarihi: {frozenMembership.FreezeEndDate?.ToString("dd/MM/yyyy")}"
                        );
                    }

                    var consolidatedMemberships = new Dictionary<string, MembershipInfo>();
                    foreach (var membership in allMemberships.Where(m => m.EndDate > now))
                    {
                        if (membershipTypes.TryGetValue(membership.MembershipTypeID, out var membershipType))
                        {
                            var branch = membershipType.Branch;
                            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);

                            if (consolidatedMemberships.ContainsKey(branch))
                            {
                                consolidatedMemberships[branch].RemainingDays += remainingDays;
                            }
                            else
                            {
                                consolidatedMemberships[branch] = new MembershipInfo
                                {
                                    Branch = branch,
                                    RemainingDays = remainingDays,
                                    StartDate = membership.StartDate,
                                    EndDate = membership.EndDate
                                };
                            }
                        }
                    }

                    var message = consolidatedMemberships.Count > 0
                        ? $"Hoşgeldiniz {member.Name.ToUpper()}. Toplam {consolidatedMemberships.Values.Sum(m => m.RemainingDays)} gün kaldı."
                        : $"Hoşgeldiniz {member.Name.ToUpper()}. Üyeliğinizin süresi dolmuş.";

                    return new SuccessDataResult<GetMemberQRByPhoneNumberDto>(
                        new GetMemberQRByPhoneNumberDto
                        {
                            Name = member.Name,
                            ScanNumber = member.ScanNumber,
                            RemainingDays = string.Join(
                                ", ",
                                consolidatedMemberships.Values
                                    .OrderByDescending(m => m.RemainingDays)
                                    .Select(m => $"{m.Branch}: {m.RemainingDays} Gün")
                            ),
                            Memberships = consolidatedMemberships.Values.ToList(),
                            IsFrozen = false,
                            FreezeEndDate = null,
                            PhoneNumber = member.PhoneNumber
                        },
                        message
                    );
                }
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<GetMemberQRByPhoneNumberDto>($"QR kod bilgileri getirilirken hata oluştu: {ex.Message}");
            }
        }

        // Helper metotlar

        private string GenerateRandomPart(int length)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            byte[] data = new byte[length];
            using (var rng = new RNGCryptoServiceProvider())
            {
                rng.GetBytes(data);
            }
            return new string(data.Select(b => chars[b % chars.Length]).ToArray());
        }

        /// <summary>
        /// Member'ın CompanyID'sini UserID ile alır (JWT token için)
        /// </summary>
        public async Task<int> GetMemberCompanyIdAsync(int userId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                
                var result = await _context.Members.AsNoTracking()
                    .Where(m => m.UserID == userId && m.IsActive == true)
                    .Select(m => m.CompanyID)
                    .FirstOrDefaultAsync(ct);
                
                return result;
            }
            catch (OperationCanceledException)
            {
                return -1;
            }
            catch (Exception)
            {
                return -1;
            }
        }

    } // EfMemberDal sınıfı kapanışı
} // namespace kapanışı
