﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Threading;

using MemberFilter = Entities.DTOs.MemberFilter;

namespace DataAccess.Abstract
{
    public interface IMemberDal:IEntityRepository<Member>
    {
        // Sync yüzeyler kaldırıldı

        // Async (CT'li) varyantlar — Sync yüzeyler korunur
        Task<PaginatedResult<Member>> GetAllPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default);
        Task<PaginatedResult<MemberFilter>> GetMemberDetailsPaginatedAsync(MemberPagingParameters parameters, CancellationToken ct = default);
        Task<PaginatedResult<Member>> GetMembersWithBalancePaginatedAsync(MemberPagingParameters parameters, string balanceFilter, CancellationToken ct = default);
        Task<PaginatedResult<MemberFilter>> GetMembersByMultiplePackagesAsync(MemberPagingParameters parameters, CancellationToken ct = default);
        Task<List<GetActiveMemberDto>> GetActiveMembersAsync(CancellationToken ct = default);

        // Legacy GetAll — Async imza (CT'li)
        Task<List<Member>> GetAllAsync(CancellationToken ct = default);


	        // Prompt 4 — Async (CT'li)
	        Task<List<MembeFilterDto>> GetMemberDetailsAsync(CancellationToken ct = default);
	        Task<List<MemberEntryExitHistoryDto>> GetMemberEntryExitHistoryAsync(CancellationToken ct = default);
	        Task<List<MemberRemainingDayDto>> GetMemberRemainingDayAsync(CancellationToken ct = default);
	        Task<List<MemberEntryDto>> GetMemberEntriesBySearchAsync(string searchText, CancellationToken ct = default);
	        Task<PaginatedResult<MemberEntryDto>> GetMemberEntriesBySearchPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default);

        Task<IResult> UpdateMemberProfileAsync(int userId, int companyId, MemberProfileUpdateDto profileUpdateDto, CancellationToken ct = default);

        // Prompt 2 — Dashboard/Metrics Async (CT'li)
        Task<IDataResult<int>> GetTotalActiveMembersAsync(int companyId, CancellationToken ct = default);
        Task<IDataResult<int>> GetTotalRegisteredMembersAsync(int companyId, CancellationToken ct = default);
        Task<IDataResult<Dictionary<string, int>>> GetActiveMemberCountsAsync(int companyId, CancellationToken ct = default);
        Task<IDataResult<Dictionary<string, int>>> GetBranchCountsAsync(int companyId, CancellationToken ct = default);
        Task<List<MemberBirthdayDto>> GetUpcomingBirthdaysAsync(int days, CancellationToken ct = default);

        // Async imzalar (CT'li) - sync yüzeyler korunur
        Task<List<MemberEntryDto>> GetTodayEntriesAsync(DateTime date, CancellationToken ct = default);
        Task<PaginatedResult<MemberEntryDto>> GetTodayEntriesPaginatedAsync(MemberEntryPagingParameters parameters, CancellationToken ct = default);

        // SOLID prensiplerine uygun refactoring için eklenen metotlar
        // Async imza (CT'li) - ScanNumber akışı
        Task<IDataResult<MemberDetailDto>> GetMemberRemainingDaysForScanNumberAsync(string scanNumber, int companyId, CancellationToken ct = default);

        // CUD Async imzalar (CT'li)
        Task<IResult> AddMemberWithUserManagementAsync(Member member, int companyId, CancellationToken ct = default);
        Task<IResult> AddMemberWithCardAsync(Member member, int companyId, CancellationToken ct = default);
        Task<IResult> DeleteMemberWithUserManagementAsync(int memberId, int companyId, CancellationToken ct = default);
        Task<IResult> UpdateMemberWithUserManagementAsync(Member member, int companyId, CancellationToken ct = default);

        // Async muadilleri (CT'li) — yeni eklenenler
        Task<IDataResult<MemberProfileDto>> GetMemberProfileByUserIdAsync(int userId, int companyId, CancellationToken ct = default);
        Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByUserIdWithoutCompanyFilterAsync(int userId, CancellationToken ct = default);
        Task<IDataResult<GetMemberQRByPhoneNumberDto>> GetMemberQRByPhoneNumberAsync(string phoneNumber, int companyId, CancellationToken ct = default);
        Task<IDataResult<MemberDetailWithHistoryDto>> GetMemberDetailByIdWithCalculationsAsync(int memberId, int companyId, CancellationToken ct = default);
        
        // JWT token için member'ın CompanyID'sini al
        Task<int> GetMemberCompanyIdAsync(int userId, CancellationToken ct = default);
    }

}
