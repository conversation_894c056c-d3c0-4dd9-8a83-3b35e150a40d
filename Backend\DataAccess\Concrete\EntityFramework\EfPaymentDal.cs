﻿using Core.DataAccess.EntityFramework;
using Core.Extensions;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Threading;


namespace DataAccess.Concrete.EntityFramework
{
    public class EfPaymentDal : EfCompanyEntityRepositoryBase<Payment, GymContext>, IPaymentDal
    {
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        // Constructor injection (Scalability için)
        public EfPaymentDal(Core.Utilities.Security.CompanyContext.ICompanyContext companyContext, GymContext context) : base(companyContext, context)
        {
            _companyContext = companyContext;
        }







        // ASYNC READ METHODS
        public async Task<List<PaymentHistoryDto>> GetPaymentHistoryAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            int companyId = _companyContext.GetCompanyId();

            var query = from a in _context.Memberships.AsNoTracking()
                        join b in _context.Payments.AsNoTracking() on a.MembershipID equals b.MemberShipID
                        join c in _context.MembershipTypes.AsNoTracking() on a.MembershipTypeID equals c.MembershipTypeID
                        join d in _context.Members.AsNoTracking() on a.MemberID equals d.MemberID
                        join rd in _context.RemainingDebts.AsNoTracking() on b.PaymentID equals rd.PaymentID into remainingDebts
                        from rd in remainingDebts.DefaultIfEmpty()
                        where b.IsActive == true
                              && d.IsActive == true
                              && a.IsActive == true
                              && b.CompanyID == companyId
                              && a.CompanyID == companyId
                              && c.CompanyID == companyId
                              && d.CompanyID == companyId
                              && (rd == null || (rd.CompanyID == companyId && rd.IsActive == true))
                        select new PaymentHistoryDto
                        {
                            PaymentID = b.PaymentID,
                            PaymentDate = b.PaymentDate,
                            PaymentAmount = b.PaymentAmount,
                            PaymentMethod = b.PaymentMethod,
                            MembershipType = c.TypeName,
                            Branch = c.Branch,
                            Name = d.Name,
                            IsActive = b.IsActive,
                            PhoneNumber = d.PhoneNumber,
                            CurrentRemainingAmount = rd != null ? rd.RemainingAmount : 0
                        };

            return await query
                .OrderByDescending(x => x.PaymentDate)
                .ThenByDescending(x => x.PaymentID)
                .ToListAsync(ct);
        }

        public async Task<List<PaymentHistoryDto>> GetDebtorMembersAsync(CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            int companyId = _companyContext.GetCompanyId();

            var query = from a in _context.Memberships.AsNoTracking()
                        join b in _context.Payments.AsNoTracking() on a.MembershipID equals b.MemberShipID
                        join c in _context.MembershipTypes.AsNoTracking() on a.MembershipTypeID equals c.MembershipTypeID
                        join d in _context.Members.AsNoTracking() on a.MemberID equals d.MemberID
                        where b.IsActive == true
                              && b.PaymentStatus == "Pending"
                              && d.IsActive == true
                              && a.IsActive == true
                              && b.CompanyID == companyId
                              && a.CompanyID == companyId
                              && c.CompanyID == companyId
                              && d.CompanyID == companyId
                        select new PaymentHistoryDto
                        {
                            PaymentID = b.PaymentID,
                            PaymentDate = b.PaymentDate,
                            PaymentAmount = b.PaymentAmount,
                            PaymentMethod = b.PaymentMethod,
                            MembershipType = c.TypeName,
                            Branch = c.Branch,
                            Name = d.Name,
                            IsActive = b.IsActive,
                            PhoneNumber = d.PhoneNumber
                        };

            return await query
                .OrderByDescending(x => x.PaymentDate)
                .ThenByDescending(x => x.PaymentID)
                .ToListAsync(ct);
        }

        public async Task<List<PaymentHistoryDto>> GetAllCombinedPaymentHistoryAsync(PaymentPagingParameters parameters, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            int companyId = _companyContext.GetCompanyId();

            var paymentsQuery = from p in _context.Payments.AsNoTracking()
                                join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                join m in _context.Members.AsNoTracking() on ms.MemberID equals m.MemberID
                                join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                where p.IsActive == true
                                    && m.IsActive == true
                                    && p.CompanyID == companyId
                                    && ms.CompanyID == companyId
                                    && m.CompanyID == companyId
                                    && mt.CompanyID == companyId
                                select new PaymentHistoryDto
                                {
                                    PaymentID = p.PaymentID,
                                    PaymentDate = p.PaymentDate,
                                    PaymentMethod = p.PaymentMethod,
                                    PaymentAmount = p.PaymentAmount,
                                    MembershipType = mt.TypeName,
                                    Branch = mt.Branch,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    IsActive = p.IsActive
                                };

            var debtPaymentsQuery = from dp in _context.DebtPayments.AsNoTracking()
                                    join rd in _context.RemainingDebts.AsNoTracking() on dp.RemainingDebtID equals rd.RemainingDebtID
                                    join p in _context.Payments.AsNoTracking() on rd.PaymentID equals p.PaymentID
                                    join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                    join m in _context.Members.AsNoTracking() on ms.MemberID equals m.MemberID
                                    join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where dp.IsActive == true
                                        && m.IsActive == true
                                        && dp.CompanyID == companyId
                                        && rd.CompanyID == companyId
                                        && p.CompanyID == companyId
                                        && ms.CompanyID == companyId
                                        && m.CompanyID == companyId
                                        && mt.CompanyID == companyId
                                    select new PaymentHistoryDto
                                    {
                                        PaymentID = dp.DebtPaymentID,
                                        PaymentDate = dp.PaymentDate,
                                        PaymentMethod = dp.PaymentMethod + " (Borç Ödemesi)",
                                        PaymentAmount = dp.PaidAmount,
                                        MembershipType = mt.TypeName,
                                        Branch = mt.Branch,
                                        Name = m.Name,
                                        PhoneNumber = m.PhoneNumber,
                                        IsActive = dp.IsActive
                                    };

            var combinedQuery = paymentsQuery.Union(debtPaymentsQuery);

            if (!string.IsNullOrWhiteSpace(parameters.SearchText))
            {
                var searchText = parameters.SearchText.ToLower();
                combinedQuery = combinedQuery.Where(x =>
                    (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                    (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));
            }

            if (parameters.StartDate.HasValue)
            {
                combinedQuery = combinedQuery.Where(x => x.PaymentDate >= parameters.StartDate.Value.Date);
            }

            if (parameters.EndDate.HasValue)
            {
                var endDateEndOfDay = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                combinedQuery = combinedQuery.Where(x => x.PaymentDate <= endDateEndOfDay);
            }

            if (!string.IsNullOrWhiteSpace(parameters.PaymentMethod))
            {
                combinedQuery = combinedQuery.Where(x => x.PaymentMethod == parameters.PaymentMethod);
            }

            combinedQuery = combinedQuery.OrderByDescending(x => x.PaymentDate).ThenByDescending(x => x.PaymentID);
            return await combinedQuery.ToListAsync(ct);
        }

        public async Task<PaginatedResult<PaymentHistoryDto>> GetPaymentHistoryPaginatedAsync(PaymentPagingParameters parameters, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            int companyId = _companyContext.GetCompanyId();

            var paymentsQuery = from p in _context.Payments.AsNoTracking()
                                join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                join m in _context.Members.AsNoTracking() on ms.MemberID equals m.MemberID
                                join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                where p.IsActive == true
                                    && m.IsActive == true
                                    && p.CompanyID == companyId
                                    && ms.CompanyID == companyId
                                    && m.CompanyID == companyId
                                    && mt.CompanyID == companyId
                                select new PaymentHistoryDto
                                {
                                    PaymentID = p.PaymentID,
                                    PaymentDate = p.PaymentDate,
                                    PaymentMethod = p.PaymentMethod,
                                    PaymentAmount = p.PaymentAmount,
                                    MembershipType = mt.TypeName,
                                    Branch = mt.Branch,
                                    Name = m.Name,
                                    PhoneNumber = m.PhoneNumber,
                                    IsActive = p.IsActive
                                };

            var debtPaymentsQuery = from dp in _context.DebtPayments.AsNoTracking()
                                    join rd in _context.RemainingDebts.AsNoTracking() on dp.RemainingDebtID equals rd.RemainingDebtID
                                    join p in _context.Payments.AsNoTracking() on rd.PaymentID equals p.PaymentID
                                    join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                    join m in _context.Members.AsNoTracking() on ms.MemberID equals m.MemberID
                                    join mt in _context.MembershipTypes.AsNoTracking() on ms.MembershipTypeID equals mt.MembershipTypeID
                                    where dp.IsActive == true
                                        && m.IsActive == true
                                        && dp.CompanyID == companyId
                                        && rd.CompanyID == companyId
                                        && p.CompanyID == companyId
                                        && ms.CompanyID == companyId
                                        && m.CompanyID == companyId
                                        && mt.CompanyID == companyId
                                    select new PaymentHistoryDto
                                    {
                                        PaymentID = dp.DebtPaymentID,
                                        PaymentDate = dp.PaymentDate,
                                        PaymentMethod = dp.PaymentMethod + " (Borç Ödemesi)",
                                        PaymentAmount = dp.PaidAmount,
                                        MembershipType = mt.TypeName,
                                        Branch = mt.Branch,
                                        Name = m.Name,
                                        PhoneNumber = m.PhoneNumber,
                                        IsActive = dp.IsActive
                                    };

            var combinedQuery = paymentsQuery.Union(debtPaymentsQuery);

            if (!string.IsNullOrWhiteSpace(parameters.SearchText))
            {
                var searchText = parameters.SearchText.ToLower();
                combinedQuery = combinedQuery.Where(x =>
                    (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                    (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));
            }

            if (parameters.StartDate.HasValue)
            {
                combinedQuery = combinedQuery.Where(x => x.PaymentDate >= parameters.StartDate.Value.Date);
            }

            if (parameters.EndDate.HasValue)
            {
                var endDateEndOfDay = parameters.EndDate.Value.Date.AddDays(1).AddTicks(-1);
                combinedQuery = combinedQuery.Where(x => x.PaymentDate <= endDateEndOfDay);
            }

            if (!string.IsNullOrWhiteSpace(parameters.PaymentMethod))
            {
                combinedQuery = combinedQuery.Where(x => x.PaymentMethod == parameters.PaymentMethod);
            }

            combinedQuery = combinedQuery.OrderByDescending(x => x.PaymentDate).ThenByDescending(x => x.PaymentID);

            var totalCount = await combinedQuery.CountAsync(ct);
            var items = await combinedQuery
                .Skip((parameters.PageNumber - 1) * parameters.PageSize)
                .Take(parameters.PageSize)
                .ToListAsync(ct);

            return new PaginatedResult<PaymentHistoryDto>(items, parameters.PageNumber, parameters.PageSize, totalCount);
        }

        // ASYNC CUD/BUSINESS OPS
        public async Task<IResult> SoftDeletePaymentAsync(int paymentId, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                var payment = await _context.Payments.FirstOrDefaultAsync(p => p.PaymentID == paymentId && p.CompanyID == companyId, ct);
                if (payment == null)
                {
                    return new ErrorResult("Ödeme bulunamadı veya erişim yetkiniz yok.");
                }

                payment.IsActive = false;
                payment.DeletedDate = DateTime.Now;
                _context.Payments.Update(payment);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Ödeme başarıyla silindi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme silinirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdatePaymentWithBusinessLogicAsync(Payment payment, int companyId, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                var existingPayment = await _context.Payments.FirstOrDefaultAsync(p => p.PaymentID == payment.PaymentID && p.CompanyID == companyId, ct);
                if (existingPayment == null)
                {
                    return new ErrorResult("Ödeme bulunamadı veya erişim yetkiniz yok.");
                }

                payment.CompanyID = companyId;
                payment.CreationDate = existingPayment.CreationDate;
                payment.UpdatedDate = DateTime.Now;

                _context.Payments.Update(payment);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Ödeme başarıyla güncellendi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdatePaymentStatusWithBusinessLogicAsync(int paymentId, string paymentMethod, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                var payment = await _context.Payments.FirstOrDefaultAsync(p => p.PaymentID == paymentId, ct);
                if (payment == null)
                {
                    return new ErrorResult("Ödeme bulunamadı.");
                }

                payment.PaymentStatus = "Completed";
                payment.PaymentMethod = paymentMethod;
                payment.UpdatedDate = DateTime.Now;
                _context.Payments.Update(payment);
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Ödeme durumu başarıyla güncellendi.");
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Ödeme durumu güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<bool> UpdatePaymentStatusAsync(int paymentId, string paymentMethod, CancellationToken ct = default)
        {
            ct.ThrowIfCancellationRequested();
            int companyId = _companyContext.GetCompanyId();

            var payment = await _context.Payments.FirstOrDefaultAsync(p => p.PaymentID == paymentId && p.CompanyID == companyId, ct);
            if (payment != null)
            {
                payment.PaymentStatus = "Completed";
                payment.PaymentMethod = paymentMethod;
                await _context.SaveChangesAsync(ct);
                return true;
            }
            return false;
        }

        public async Task<PaymentTotals> GetPaymentTotalsAsync(PaymentPagingParameters parameters, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                int companyId = _companyContext.GetCompanyId();

                var paymentsQuery = from p in _context.Payments.AsNoTracking()
                                    join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                    join m in _context.Members.AsNoTracking() on ms.MemberID equals m.MemberID
                                    where p.IsActive == true
                                        && p.OriginalPaymentMethod != "Borç"
                                        && m.IsActive == true
                                        && p.CompanyID == companyId
                                        && ms.CompanyID == companyId
                                        && m.CompanyID == companyId
                                    select new
                                    {
                                        p.OriginalPaymentMethod,
                                        p.PaymentAmount,
                                        p.PaymentDate,
                                        m.Name,
                                        m.PhoneNumber
                                    };

                var debtPaymentsQuery = from dp in _context.DebtPayments.AsNoTracking()
                                        join rd in _context.RemainingDebts.AsNoTracking() on dp.RemainingDebtID equals rd.RemainingDebtID
                                        join p in _context.Payments.AsNoTracking() on rd.PaymentID equals p.PaymentID
                                        join ms in _context.Memberships.AsNoTracking() on p.MemberShipID equals ms.MembershipID
                                        join m in _context.Members.AsNoTracking() on ms.MemberID equals m.MemberID
                                        where dp.IsActive == true
                                            && m.IsActive == true
                                            && dp.CompanyID == companyId
                                            && rd.CompanyID == companyId
                                            && p.CompanyID == companyId
                                            && ms.CompanyID == companyId
                                            && m.CompanyID == companyId
                                        select new
                                        {
                                            PaymentMethod = dp.PaymentMethod,
                                            PaymentAmount = dp.PaidAmount,
                                            dp.PaymentDate,
                                            m.Name,
                                            m.PhoneNumber
                                        };

                if (!string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var searchText = parameters.SearchText.ToLower();
                    paymentsQuery = paymentsQuery.Where(x =>
                        (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                        (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));

                    debtPaymentsQuery = debtPaymentsQuery.Where(x =>
                        (x.Name != null && x.Name.ToLower().Contains(searchText)) ||
                        (x.PhoneNumber != null && x.PhoneNumber.Contains(searchText)));
                }

                if (parameters.StartDate.HasValue)
                {
                    var startDate = parameters.StartDate.Value.Date;
                    var endDateExclusive = (parameters.EndDate?.Date ?? DateTime.Today).AddDays(1);

                    paymentsQuery = paymentsQuery.Where(x =>
                        x.PaymentDate >= startDate && x.PaymentDate < endDateExclusive);

                    debtPaymentsQuery = debtPaymentsQuery.Where(x =>
                        x.PaymentDate >= startDate && x.PaymentDate < endDateExclusive);
                }
                else if (!parameters.EndDate.HasValue && string.IsNullOrWhiteSpace(parameters.SearchText))
                {
                    var today = DateTime.Today;
                    var firstDayOfMonth = new DateTime(today.Year, today.Month, 1);
                    var nextMonthStartDateForTotals = firstDayOfMonth.AddMonths(1);

                    paymentsQuery = paymentsQuery.Where(x =>
                        x.PaymentDate >= firstDayOfMonth && x.PaymentDate < nextMonthStartDateForTotals);

                    debtPaymentsQuery = debtPaymentsQuery.Where(x =>
                        x.PaymentDate >= firstDayOfMonth && x.PaymentDate < nextMonthStartDateForTotals);
                }

                var normalPayments = await paymentsQuery
                    .GroupBy(x => 1)
                    .Select(g => new
                    {
                        Cash = g.Where(x => x.OriginalPaymentMethod == "Nakit").Sum(x => x.PaymentAmount),
                        CreditCard = g.Where(x => x.OriginalPaymentMethod == "Kredi Kartı").Sum(x => x.PaymentAmount),
                        Transfer = g.Where(x => x.OriginalPaymentMethod == "Havale - EFT").Sum(x => x.PaymentAmount)
                    })
                    .FirstOrDefaultAsync(ct) ?? new { Cash = 0m, CreditCard = 0m, Transfer = 0m };

                var debtPayments = await debtPaymentsQuery
                    .GroupBy(x => 1)
                    .Select(g => new
                    {
                        Cash = g.Where(x => x.PaymentMethod == "Nakit").Sum(x => x.PaymentAmount),
                        CreditCard = g.Where(x => x.PaymentMethod == "Kredi Kartı").Sum(x => x.PaymentAmount),
                        Transfer = g.Where(x => x.PaymentMethod == "Havale - EFT").Sum(x => x.PaymentAmount)
                    })
                    .FirstOrDefaultAsync(ct) ?? new { Cash = 0m, CreditCard = 0m, Transfer = 0m };

                var totalRemainingDebt = await _context.RemainingDebts
                    .Where(rd => rd.IsActive == true && rd.CompanyID == companyId)
                    .SumAsync(rd => (decimal?)rd.RemainingAmount, ct) ?? 0m;

                return new PaymentTotals
                {
                    Cash = normalPayments.Cash + debtPayments.Cash,
                    CreditCard = normalPayments.CreditCard + debtPayments.CreditCard,
                    Transfer = normalPayments.Transfer + debtPayments.Transfer,
                    Debt = totalRemainingDebt
                };
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new PaymentTotals { Cash = 0, CreditCard = 0, Transfer = 0, Debt = 0 };
            }
        }

        public async Task<MonthlyRevenueDto> GetMonthlyRevenueAsync(int year, CancellationToken ct = default)
        {
            try
            {
                ct.ThrowIfCancellationRequested();
                int companyId = _companyContext.GetCompanyId();

                string[] turkishMonths = { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" };
                var result = new MonthlyRevenueDto
                {
                    Year = year,
                    Months = turkishMonths.ToList()
                };

                var startOfYear = new DateTime(year, 1, 1);
                var startOfNextYear = startOfYear.AddYears(1);

                var normalPaymentsByMonthQuery = _context.Payments
                    .AsNoTracking()
                    .Where(p => p.IsActive == true
                                && p.CompanyID == companyId
                                && p.OriginalPaymentMethod != "Borç"
                                && p.PaymentDate >= startOfYear
                                && p.PaymentDate < startOfNextYear)
                    .GroupBy(p => p.PaymentDate.Month)
                    .Select(g => new { Month = g.Key, Amount = g.Sum(p => (decimal?)p.PaymentAmount) ?? 0m });

                var debtPaymentsByMonthQuery = _context.DebtPayments
                    .AsNoTracking()
                    .Where(dp => dp.IsActive == true
                                 && dp.CompanyID == companyId
                                 && dp.PaymentDate >= startOfYear
                                 && dp.PaymentDate < startOfNextYear)
                    .GroupBy(dp => dp.PaymentDate.Month)
                    .Select(g => new { Month = g.Key, Amount = g.Sum(dp => (decimal?)dp.PaidAmount) ?? 0m });

                var combined = await normalPaymentsByMonthQuery
                    .Concat(debtPaymentsByMonthQuery)
                    .GroupBy(x => x.Month)
                    .Select(g => new { Month = g.Key, Amount = g.Sum(x => x.Amount) })
                    .ToListAsync(ct);

                var totalsByMonth = combined.ToDictionary(x => x.Month, x => x.Amount);

                var monthlyRevenues = new List<decimal>(capacity: 12);
                for (int month = 1; month <= 12; month++)
                {
                    monthlyRevenues.Add(totalsByMonth.TryGetValue(month, out var sum) ? sum : 0m);
                }

                result.MonthlyRevenue = monthlyRevenues;
                return result;
            }
            catch (OperationCanceledException)
            {
                throw;
            }
            catch (Exception ex)
            {
                return new MonthlyRevenueDto
                {
                    Year = year,
                    Months = new string[] { "Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" }.ToList(),
                    MonthlyRevenue = Enumerable.Repeat(0m, 12).ToList()
                };
            }
        }




    }
}
