﻿using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using System.Threading;

namespace DataAccess.Abstract
{
    public interface ILicensePackageDal : IEntityRepository<LicensePackage>
    {

        // Async imzalar (CT ile)
        Task<IResult> AddLicensePackageAsync(LicensePackage licensePackage, CancellationToken ct = default);
        Task<IResult> UpdateLicensePackageAsync(LicensePackage licensePackage, CancellationToken ct = default);
        Task<IResult> SoftDeleteLicensePackageAsync(int licensePackageId, CancellationToken ct = default);
    }



}
