﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface ICompanyUserDal:IEntityRepository<CompanyUser>
    {

        // Async imzalar (CT zorunlu)
        Task<List<CompanyDetailDto>> GetCompanyDetailsAsync(CancellationToken ct = default);
        Task<List<CompanyDetailDto>> GetCompanyUserDetailsByCityIdAsync(int cityId, CancellationToken ct = default);
        Task<List<CompanyUserDetailDto>> GetCompanyUserDetailsAsync(CancellationToken ct = default);
        Task<CompanyUserFullDetailDto> GetCompanyUserFullDetailsAsync(int companyUserID, CancellationToken ct = default);
        Task<PaginatedCompanyUserDto> GetCompanyUsersPaginatedAsync(int pageNumber, int pageSize, string searchTerm = "", CancellationToken ct = default);
        Task<IResult> SoftDeleteCompanyUserBasicAsync(int companyUserID, CancellationToken ct = default);
        Task<List<DeletedCompanyUserDto>> GetDeletedCompanyUsersAsync(CancellationToken ct = default);
        Task<IResult> RestoreCompanyUserBasicAsync(int companyUserID, CancellationToken ct = default);
        Task<IResult> UpdateCompanyUserFullAsync(CompanyUserFullUpdateDto updateDto, CancellationToken ct = default);
    }
}
