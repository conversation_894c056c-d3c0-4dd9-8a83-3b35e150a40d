using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfExerciseCategoryDal : EfEntityRepositoryBase<ExerciseCategory, GymContext>, IExerciseCategoryDal
    {
        // Constructor injection (Scalability için)
        public EfExerciseCategoryDal(GymContext context) : base(context)
        {
        }


        // Async implementasyonlar
        public async Task<List<ExerciseCategoryDto>> GetAllCategoriesAsync(CancellationToken ct = default)
        {
            var query = from ec in _context.ExerciseCategories.AsNoTracking()
                        orderby ec.CategoryName
                        select new ExerciseCategoryDto
                        {
                            ExerciseCategoryID = ec.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            Description = ec.Description,
                            IsActive = ec.IsActive,
                            CreationDate = ec.CreationDate
                        };
            return await query.ToListAsync(ct);
        }

        public async Task<List<ExerciseCategoryDto>> GetActiveCategoriesAsync(CancellationToken ct = default)
        {
            var query = from ec in _context.ExerciseCategories.AsNoTracking()
                        where ec.IsActive == true
                        orderby ec.CategoryName
                        select new ExerciseCategoryDto
                        {
                            ExerciseCategoryID = ec.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            Description = ec.Description,
                            IsActive = ec.IsActive,
                            CreationDate = ec.CreationDate
                        };
            return await query.ToListAsync(ct);
        }

        public async Task<ExerciseCategoryDto> GetCategoryByIdAsync(int categoryId, CancellationToken ct = default)
        {
            var query = from ec in _context.ExerciseCategories.AsNoTracking()
                        where ec.ExerciseCategoryID == categoryId
                        select new ExerciseCategoryDto
                        {
                            ExerciseCategoryID = ec.ExerciseCategoryID,
                            CategoryName = ec.CategoryName,
                            Description = ec.Description,
                            IsActive = ec.IsActive,
                            CreationDate = ec.CreationDate
                        };
            return await query.FirstOrDefaultAsync(ct);
        }

        public async Task<IResult> AddExerciseCategoryAsync(ExerciseCategoryAddDto categoryAddDto, CancellationToken ct = default)
        {
            try
            {
                var category = new ExerciseCategory
                {
                    CategoryName = categoryAddDto.CategoryName,
                    Description = categoryAddDto.Description,
                    IsActive = true,
                    CreationDate = DateTime.Now
                };

                await AddAsync(category, ct);
                return new SuccessResult("Egzersiz kategorisi başarıyla eklendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateExerciseCategoryAsync(ExerciseCategoryUpdateDto categoryUpdateDto, CancellationToken ct = default)
        {
            try
            {
                var existingCategory = await GetAsync(c => c.ExerciseCategoryID == categoryUpdateDto.ExerciseCategoryID, ct);
                if (existingCategory == null)
                {
                    return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                }

                existingCategory.CategoryName = categoryUpdateDto.CategoryName;
                existingCategory.Description = categoryUpdateDto.Description;
                existingCategory.IsActive = categoryUpdateDto.IsActive;
                existingCategory.UpdatedDate = DateTime.Now;

                await UpdateAsync(existingCategory, ct);
                return new SuccessResult("Egzersiz kategorisi başarıyla güncellendi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> DeleteExerciseCategoryAsync(int categoryId, CancellationToken ct = default)
        {
            try
            {
                var category = await GetAsync(c => c.ExerciseCategoryID == categoryId, ct);
                if (category == null)
                {
                    return new ErrorResult("Egzersiz kategorisi bulunamadı.");
                }

                // Soft delete
                category.IsActive = false;
                category.DeletedDate = DateTime.Now;

                await UpdateAsync(category, ct);
                return new SuccessResult("Egzersiz kategorisi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Egzersiz kategorisi silinirken hata oluştu: {ex.Message}");
            }
        }
    }
}
