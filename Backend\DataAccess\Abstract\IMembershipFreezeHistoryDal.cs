﻿using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IMembershipFreezeHistoryDal : IEntityRepository<MembershipFreezeHistory>
    {
        // Async imzalar (CancellationToken ile)
        Task<List<MembershipFreezeHistoryDto>> GetFreezeHistoryDetailsAsync(CancellationToken ct = default);
        Task<List<MembershipFreezeHistoryDto>> GetFreezeHistoryByMembershipIdAsync(int membershipId, CancellationToken ct = default);
        Task<int> GetTotalFreezeDaysUsedInLastYearAsync(int membershipId, CancellationToken ct = default);
        Task<int> GetRemainingFreezeDaysWithCalculationAsync(int membershipId, CancellationToken ct = default);
    }

}
