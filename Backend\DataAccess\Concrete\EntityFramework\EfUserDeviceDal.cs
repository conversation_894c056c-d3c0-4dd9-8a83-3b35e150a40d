using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserDeviceDal : EfEntityRepositoryBase<UserDevice, GymContext>, IUserDeviceDal
    {
        // Constructor injection (Scalability i�in)
        public EfUserDeviceDal(GymContext context) : base(context)
        {
        }


        public void CleanExpiredTokens()
        {
            if (_context != null)
            {
                // DI kullan�l�yor - Scalability optimized
                var expiredDevices = _context.UserDevices.Where(d =>
                    d.IsActive == true &&
                    d.RefreshTokenExpiration.HasValue &&
                    d.RefreshTokenExpiration.Value < DateTime.Now).ToList();

                foreach (var device in expiredDevices)
                {
                    device.IsActive = false;
                    device.RefreshToken = null;
                    device.RefreshTokenExpiration = null;
                    _context.UserDevices.Update(device);
                }

                if (expiredDevices.Any())
                {
                    _context.SaveChanges();
                }
                return;
            }

            // DI kullan�lm�yorsa exception f�rlat (art�k backward compatibility yok)
            throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");
        }





        /// <summary>
        /// SOLID prensiplerine uygun: Validation ve entity manipulation logic DAL katman�nda
        /// </summary>




        // ASYNC versiyonlar
        public async Task<List<UserDevice>> GetActiveDevicesByUserIdAsync(int userId, CancellationToken ct = default)
        {
            return await _context.UserDevices
                .AsNoTracking()
                .Where(d => d.UserId == userId && d.IsActive)
                .ToListAsync(ct);
        }

        public async Task<UserDevice> GetByRefreshTokenAsync(string refreshToken, CancellationToken ct = default)
        {
            return await _context.UserDevices
                .AsNoTracking()
                .FirstOrDefaultAsync(d => d.RefreshToken == refreshToken && d.IsActive, ct);
        }

        public async Task<IResult> AddDeviceWithManagementAsync(UserDevice device, CancellationToken ct = default)
        {
            if (_context == null)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            ct.ThrowIfCancellationRequested();

            const int MAX_ACTIVE_DEVICES = 5; // Web için
            const int MAX_MOBILE_DEVICES = 1; // Mobil için sadece 1 cihaz

            await CleanExpiredTokensAsync(ct);

            var activeDevices = await _context.UserDevices
                .Where(d => d.UserId == device.UserId && d.IsActive)
                .ToListAsync(ct);

            bool isMobileDevice = device.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                                  device.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                                  device.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                                  device.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true;

            if (isMobileDevice)
            {
                var existingMobileDevices = activeDevices.Where(d =>
                    d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                    d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                    d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                    d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true).ToList();

                foreach (var existingDevice in existingMobileDevices)
                {
                    existingDevice.IsActive = false;
                    existingDevice.RefreshToken = null;
                    existingDevice.RefreshTokenExpiration = null;
                    _context.UserDevices.Update(existingDevice);
                }
            }
            else
            {
                var webDevices = activeDevices.Where(d =>
                    !(d.DeviceInfo?.Contains("Mobile", StringComparison.OrdinalIgnoreCase) == true ||
                      d.DeviceInfo?.Contains("Android", StringComparison.OrdinalIgnoreCase) == true ||
                      d.DeviceInfo?.Contains("iOS", StringComparison.OrdinalIgnoreCase) == true ||
                      d.DeviceInfo?.Contains("Flutter", StringComparison.OrdinalIgnoreCase) == true)).ToList();

                if (webDevices.Count >= MAX_ACTIVE_DEVICES)
                {
                    var oldestDevice = webDevices
                        .OrderBy(d => d.CreatedAt)
                        .First();
                    oldestDevice.IsActive = false;
                    oldestDevice.RefreshToken = null;
                    oldestDevice.RefreshTokenExpiration = null;
                    _context.UserDevices.Update(oldestDevice);
                }
            }

            await _context.UserDevices.AddAsync(device, ct);
            await _context.SaveChangesAsync(ct);
            return new SuccessResult();
        }

        public async Task CleanExpiredTokensAsync(CancellationToken ct = default)
        {
            if (_context == null)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            var expiredDevices = await _context.UserDevices.Where(d =>
                d.IsActive == true &&
                d.RefreshTokenExpiration.HasValue &&
                d.RefreshTokenExpiration.Value < DateTime.Now).ToListAsync(ct);

            foreach (var device in expiredDevices)
            {
                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;
                _context.UserDevices.Update(device);
            }

            if (expiredDevices.Any())
            {
                await _context.SaveChangesAsync(ct);
            }
        }

        public async Task<IResult> RevokeAllDevicesExceptCurrentAsync(int userId, string currentRefreshToken, CancellationToken ct = default)
        {
            if (_context == null)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            var devices = await _context.UserDevices.Where(d =>
                d.UserId == userId &&
                d.IsActive == true &&
                d.RefreshToken != currentRefreshToken).ToListAsync(ct);

            foreach (var device in devices)
            {
                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;
                _context.UserDevices.Update(device);
            }

            if (devices.Any())
            {
                await _context.SaveChangesAsync(ct);
            }

            return new SuccessResult();
        }

        public async Task<IDataResult<UserDevice>> GetByRefreshTokenWithValidationAsync(string refreshToken, CancellationToken ct = default)
        {
            if (_context == null)
                throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

            var device = await _context.UserDevices
                .FirstOrDefaultAsync(d => d.RefreshToken == refreshToken && d.IsActive, ct);

            if (device == null)
                return new ErrorDataResult<UserDevice>("Geçersiz refresh token");

            if (device.RefreshTokenExpiration.HasValue && device.RefreshTokenExpiration.Value < DateTime.Now)
            {
                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;
                _context.UserDevices.Update(device);
                await _context.SaveChangesAsync(ct);
                return new ErrorDataResult<UserDevice>("Süresi dolmuş refresh token");
            }

            return new SuccessDataResult<UserDevice>(device);
        }

        public async Task<IResult> RevokeDeviceWithValidationAsync(int deviceId, CancellationToken ct = default)
        {
            try
            {
                if (_context == null)
                    throw new InvalidOperationException("DbContext is not available. Dependency injection must be used.");

                var device = await _context.UserDevices.FirstOrDefaultAsync(d => d.Id == deviceId, ct);
                if (device == null)
                {
                    return new ErrorResult("Cihaz bulunamadı");
                }

                device.IsActive = false;
                device.RefreshToken = null;
                device.RefreshTokenExpiration = null;

                await _context.SaveChangesAsync(ct);
                return new SuccessResult();
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Cihaz iptal edilirken hata oluştu: {ex.Message}");
            }
        }



    }
}
