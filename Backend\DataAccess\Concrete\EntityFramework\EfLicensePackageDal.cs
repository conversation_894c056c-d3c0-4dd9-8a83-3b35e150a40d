﻿using Core.DataAccess.EntityFramework;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using Microsoft.EntityFrameworkCore;


namespace DataAccess.Concrete.EntityFramework
{
    public class EfLicensePackageDal : EfEntityRepositoryBase<LicensePackage, GymContext>, ILicensePackageDal
    {
        // Constructor injection (Scalability için)
        public EfLicensePackageDal(GymContext context) : base(context)
        {
        }


        // Async implementasyonlar
        public async Task<IResult> AddLicensePackageAsync(LicensePackage licensePackage, CancellationToken ct = default)
        {
            try
            {
                licensePackage.CreationDate = DateTime.Now;
                licensePackage.IsActive = true;

                await AddAsync(licensePackage, ct);
                return new SuccessResult("Lisans paketi başar<PERSON>yla eklendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans paketi eklenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> UpdateLicensePackageAsync(LicensePackage licensePackage, CancellationToken ct = default)
        {
            try
            {
                licensePackage.UpdatedDate = DateTime.Now;

                await UpdateAsync(licensePackage, ct);
                return new SuccessResult("Lisans paketi başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans paketi güncellenirken hata oluştu: {ex.Message}");
            }
        }

        public async Task<IResult> SoftDeleteLicensePackageAsync(int licensePackageId, CancellationToken ct = default)
        {
            try
            {
                var licensePackage = await _context.LicensePackages.FirstOrDefaultAsync(lp => lp.LicensePackageID == licensePackageId, ct);
                if (licensePackage == null)
                {
                    return new ErrorResult("Lisans paketi bulunamadı.");
                }

                licensePackage.IsActive = false;
                licensePackage.DeletedDate = DateTime.Now;
                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Lisans paketi başarıyla silindi.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Lisans paketi silinirken hata oluştu: {ex.Message}");
            }
        }
    }

}
