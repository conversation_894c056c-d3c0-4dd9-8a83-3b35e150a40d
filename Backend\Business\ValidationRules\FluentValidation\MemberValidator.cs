﻿﻿using Core.Utilities.IoC;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using FluentValidation;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Business.ValidationRules.FluentValidation
{
    public class MemberValidator : AbstractValidator<Member>
    {
        private readonly ICompanyContext _companyContext;
        private readonly IMemberDal _memberDal;

        public MemberValidator()
        {
            // ServiceTool üzerinden servisleri al
            _companyContext = ServiceTool.ServiceProvider?.GetService<ICompanyContext>();
            _memberDal = ServiceTool.ServiceProvider?.GetService<IMemberDal>();

            RuleFor(x => x.Name).NotEmpty().WithMessage("Ad Soyad kısmı boş bırakılamaz.");
            RuleFor(x => x.Gender).NotEmpty().WithMessage("Cinsiyet kısmı boş bırakılamaz.");
            RuleFor(x => x.PhoneNumber).NotEmpty().WithMessage("Telefon numarası kısmı boş bırakılamaz.");
            RuleFor(p => p.PhoneNumber).MustAsync(async (member, phone, ct) => await BeUniquePhoneNumberAsync(member, ct)).WithMessage("Bu telefon numarası bu salonda zaten kayıtlı.");
            RuleFor(p => p.PhoneNumber).Must(StartsWithZero).WithMessage("Telefon numarası 0 ile başlamak zorundadır");
            RuleFor(p => p.PhoneNumber).Length(11).WithMessage("Telefon numarasını kontrol ediniz.");

            // E-posta adresi için validasyon kuralları
            RuleFor(p => p.Email).EmailAddress().When(p => !string.IsNullOrEmpty(p.Email)).WithMessage("Geçerli bir e-posta adresi giriniz.");
            RuleFor(p => p.Email).MustAsync(async (member, email, ct) => await BeUniqueEmailInCompanyAsync(member, ct)).When(p => !string.IsNullOrEmpty(p.Email)).WithMessage("Bu e-posta adresi bu salonda başka bir üye için zaten kullanılıyor.");
        }

        private async Task<bool> BeUniquePhoneNumberAsync(Member member, CancellationToken ct)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyContext == null || _memberDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("MemberValidator: Dependencies are null - phone validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            // Şirket ID'sini ICompanyContext servisinden al
            int companyId = _companyContext.GetCompanyId();

            if (member.MemberID != 0)
            {
                // Mevcut üye güncelleme durumu
                var list = await _memberDal.GetAllAsync(u =>
                    u.PhoneNumber == member.PhoneNumber &&
                    u.MemberID != member.MemberID &&
                    u.IsActive == true &&
                    u.CompanyID == companyId, ct);
                return !list.Any(); // Aynı şirket içinde kontrol et
            }
            // Yeni üye ekleme durumu
            var listNew = await _memberDal.GetAllAsync(u =>
                u.PhoneNumber == member.PhoneNumber &&
                u.IsActive == true &&
                u.CompanyID == companyId, ct);
            return !listNew.Any(); // Aynı şirket içinde kontrol et
        }

        private bool StartsWithZero(string arg)
        {
            return arg.StartsWith("0");
        }

        private async Task<bool> BeUniqueEmailInCompanyAsync(Member member, CancellationToken ct)
        {
            // Null check for DI services - 1000+ salon sisteminde kritik
            if (_companyContext == null || _memberDal == null)
            {
                // Log the issue for monitoring in production
                System.Diagnostics.Debug.WriteLine("MemberValidator: Dependencies are null - email validation bypassed");
                return true; // Validation geçer, başka katmanda kontrol edilir
            }

            // DI kullanılıyor - Scalability optimized
            // Şirket ID'sini ICompanyContext servisinden al
            int companyId = _companyContext.GetCompanyId();

            if (member.MemberID != 0)
            {
                // Mevcut üye güncelleme durumu
                var list = await _memberDal.GetAllAsync(u =>
                    u.Email == member.Email &&
                    u.MemberID != member.MemberID &&
                    u.IsActive == true &&
                    u.CompanyID == companyId, ct);
                return !list.Any(); // Aynı şirket içinde kontrol et
            }
            // Yeni üye ekleme durumu
            var listNew = await _memberDal.GetAllAsync(u =>
                u.Email == member.Email &&
                u.IsActive == true &&
                u.CompanyID == companyId, ct);
            return !listNew.Any(); // Aynı şirket içinde kontrol et
        }
    }
}