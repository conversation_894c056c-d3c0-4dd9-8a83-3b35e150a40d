using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;


namespace DataAccess.Abstract
{
    public interface IExpenseDal : IEntityRepository<Expense>
    {

        // Async imzalar (CancellationToken ile) — sync yüzeyler korunur
        Task<PaginatedResult<ExpenseDto>> GetExpensesPaginatedAsync(ExpensePagingParameters parameters, CancellationToken ct = default);
        Task<List<ExpenseDto>> GetAllExpensesFilteredAsync(ExpensePagingParameters parameters, CancellationToken ct = default);
        Task<List<ExpenseDto>> GetExpensesByDateRangeAsync(DateTime startDate, DateTime endDate, CancellationToken ct = default);
        Task<ExpenseDashboardDto> GetExpenseDashboardDataAsync(int year, int month, CancellationToken ct = default);
        Task<MonthlyExpenseDto> GetMonthlyExpenseAsync(int year, CancellationToken ct = default);
        Task<ExpenseTotals> GetExpenseTotalsAsync(ExpensePagingParameters parameters, CancellationToken ct = default);

        Task<Core.Utilities.Results.IResult> SoftDeleteExpenseAsync(int expenseId, int companyId, CancellationToken ct = default);
        Task<Core.Utilities.Results.IResult> AddExpenseWithBusinessLogicAsync(Expense expense, int companyId, CancellationToken ct = default);
        Task<Core.Utilities.Results.IResult> UpdateExpenseWithBusinessLogicAsync(Expense expense, int companyId, CancellationToken ct = default);
    }
}