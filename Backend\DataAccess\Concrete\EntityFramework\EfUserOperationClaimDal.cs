﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserOperationClaimDal : EfEntityRepositoryBase<UserOperationClaim, GymContext>, IUserOperationClaimDal
    {
        // Constructor injection (Scalability için)
        public EfUserOperationClaimDal(GymContext context) : base(context)
        {
        }


        public async Task<List<UserOperationClaimDto>> GetUserOperationClaimDetailsAsync(CancellationToken ct = default)
        {
            var query = from uoc in _context.UserOperationClaims.AsNoTracking()
                        join u in _context.Users.AsNoTracking() on uoc.UserId equals u.UserID
                        join oc in _context.OperationClaims.AsNoTracking() on uoc.OperationClaimId equals oc.OperationClaimId
                        select new UserOperationClaimDto
                        {
                            UserOperationClaimId = uoc.UserOperationClaimId,
                            UserId = uoc.UserId,
                            UserName = u.FirstName + " " + u.LastName,
                            OperationClaimId = uoc.OperationClaimId,
                            OperationClaimName = oc.Name,
                            Email = u.Email
                        };
            return await query.ToListAsync(ct);
        }


        public async Task<IResult> InvalidateUserTokensByUserIdAsync(int userId, CancellationToken ct = default)
        {
            try
            {
                var userDevices = await _context.UserDevices
                    .Where(ud => ud.UserId == userId && ud.IsActive == true)
                    .ToListAsync(ct);

                foreach (var device in userDevices)
                {
                    device.RefreshTokenExpiration = DateTime.Now;
                }

                await _context.SaveChangesAsync(ct);

                return new SuccessResult("Kullanıcı token'ları başarıyla geçersiz kılındı.");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Token geçersiz kılma işlemi sırasında hata oluştu: {ex.Message}");
            }
        }
    }
}
