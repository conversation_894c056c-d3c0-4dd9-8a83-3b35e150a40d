using Core.DataAccess;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IExerciseCategoryDal : IEntityRepository<ExerciseCategory>
    {
        // Async imzalar (CancellationToken ile)
        Task<List<ExerciseCategoryDto>> GetAllCategoriesAsync(CancellationToken ct = default);
        Task<List<ExerciseCategoryDto>> GetActiveCategoriesAsync(CancellationToken ct = default);
        Task<ExerciseCategoryDto> GetCategoryByIdAsync(int categoryId, CancellationToken ct = default);
        Task<IResult> AddExerciseCategoryAsync(ExerciseCategoryAddDto categoryAddDto, CancellationToken ct = default);
        Task<IResult> UpdateExerciseCategoryAsync(ExerciseCategoryUpdateDto categoryUpdateDto, CancellationToken ct = default);
        Task<IResult> DeleteExerciseCategoryAsync(int categoryId, CancellationToken ct = default);
    }
}
